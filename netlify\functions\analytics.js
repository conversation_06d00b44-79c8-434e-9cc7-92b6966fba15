const { MongoClient } = require('mongodb');

let cachedClient = null;

async function connectToDatabase() {
    if (cachedClient) {
        return cachedClient;
    }

    const mongoUri = process.env.MONGODB_URI;
    
    if (!mongoUri) {
        throw new Error('MongoDB URI not found in environment variables');
    }

    const client = new MongoClient(mongoUri);
    await client.connect();
    cachedClient = client;
    return client;
}

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    };

    // <PERSON>le preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    if (event.httpMethod === 'POST') {
        try {
            const { type, page, userAgent, referrer } = JSON.parse(event.body);

            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const collection = db.collection('analytics');

            const analyticsData = {
                type: type || 'page_view',
                page: page || '/',
                userAgent: userAgent || event.headers['user-agent'] || 'unknown',
                referrer: referrer || event.headers.referer || 'direct',
                ip: event.headers['x-forwarded-for'] || event.headers['x-real-ip'] || 'unknown',
                timestamp: new Date(),
                date: new Date().toISOString().split('T')[0]
            };

            await collection.insertOne(analyticsData);

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Analytics data recorded'
                }),
            };

        } catch (error) {
            console.error('Analytics error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Server error occurred'
                }),
            };
        }
    }

    if (event.httpMethod === 'GET') {
        try {
            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const analyticsCollection = db.collection('analytics');
            const contactsCollection = db.collection('contacts');
            const portfolioCollection = db.collection('portfolio');

            // Get overview statistics
            const totalVisits = await analyticsCollection.countDocuments();
            const todayVisits = await analyticsCollection.countDocuments({
                date: new Date().toISOString().split('T')[0]
            });
            const totalContacts = await contactsCollection.countDocuments();
            const portfolioCount = await portfolioCollection.countDocuments();

            // Get page statistics
            const pageStats = await analyticsCollection.aggregate([
                { $group: { _id: '$page', count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 10 }
            ]).toArray();

            // Get recent visits
            const recentVisits = await analyticsCollection
                .find({})
                .sort({ timestamp: -1 })
                .limit(10)
                .toArray();

            // Get daily visits for the last 7 days
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            
            const dailyVisits = await analyticsCollection.aggregate([
                { 
                    $match: { 
                        timestamp: { $gte: sevenDaysAgo } 
                    } 
                },
                { 
                    $group: { 
                        _id: '$date', 
                        count: { $sum: 1 } 
                    } 
                },
                { $sort: { _id: 1 } }
            ]).toArray();

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    data: {
                        overview: {
                            totalVisits,
                            todayVisits,
                            totalContacts,
                            portfolioCount
                        },
                        pageStats,
                        recentVisits,
                        dailyVisits
                    }
                }),
            };

        } catch (error) {
            console.error('Get analytics error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Server error occurred'
                }),
            };
        }
    }

    return {
        statusCode: 405,
        headers,
        body: JSON.stringify({
            success: false,
            error: 'Method not allowed'
        }),
    };
};
