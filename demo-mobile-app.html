<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskMaster Pro - Mobil Uygulama | Broxy Code Demo</title>
    <meta name="description" content="TaskMaster Pro mobil uygulaması tanıtım sitesi - Broxy Code tarafından geliştirildi">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #ff6b35;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }
        
        .phone-mockup {
            max-width: 300px;
            margin: 0 auto;
            position: relative;
        }
        
        .phone-frame {
            background: #333;
            border-radius: 30px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .phone-screen {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            aspect-ratio: 9/19.5;
        }
        
        .app-store-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .store-button {
            display: inline-block;
            transition: transform 0.3s ease;
        }
        
        .store-button:hover {
            transform: translateY(-5px);
        }
        
        .store-button img {
            height: 60px;
            border-radius: 10px;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .stats-section {
            background: var(--dark-color);
            color: white;
            padding: 80px 0;
        }
        
        .stat-item {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--accent-color);
            display: block;
        }
        
        .testimonial-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .rating {
            color: #ffc107;
            margin-bottom: 15px;
        }
        
        .cta-section {
            background: linear-gradient(135deg, var(--accent-color) 0%, #ff8c5a 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .btn-download {
            background: white;
            color: var(--accent-color);
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn-download:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: var(--accent-color);
        }
        
        .back-to-portfolio {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255,255,255,0.9);
            color: var(--dark-color);
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-to-portfolio:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: var(--dark-color);
        }
        
        @media (max-width: 768px) {
            .hero-section {
                padding: 60px 0;
            }
            
            .app-store-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Back to Portfolio Button -->
    <a href="index.html#portfolio" class="back-to-portfolio">
        <i class="fas fa-arrow-left me-2"></i>Portfolio'ya Dön
    </a>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">TaskMaster Pro</h1>
                    <h2 class="h3 mb-4">Görevlerinizi Yönetin, Hedeflerinize Ulaşın</h2>
                    <p class="lead mb-4">
                        Yapay zeka destekli görev yönetimi ile verimliliğinizi artırın. 
                        TaskMaster Pro ile günlük işlerinizi organize edin ve hedeflerinize daha hızlı ulaşın.
                    </p>
                    
                    <div class="app-store-buttons">
                        <a href="#" class="store-button">
                            <img src="https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg" alt="App Store'dan İndir">
                        </a>
                        <a href="#" class="store-button">
                            <img src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png" alt="Google Play'den İndir">
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="phone-mockup">
                        <div class="phone-frame">
                            <div class="phone-screen">
                                <img src="https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&w=300&h=600&fit=crop" 
                                     alt="TaskMaster Pro App Screenshot" class="img-fluid">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-lg-8 mx-auto">
                    <h2 class="display-5 fw-bold mb-4">Özellikler</h2>
                    <p class="lead text-muted">TaskMaster Pro ile hayatınızı kolaylaştıran güçlü özellikler</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4>Yapay Zeka Desteği</h4>
                        <p>AI algoritmaları ile görevlerinizi önceliklendirir ve en verimli çalışma planını oluşturur.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-sync"></i>
                        </div>
                        <h4>Çoklu Platform Senkronizasyonu</h4>
                        <p>Tüm cihazlarınızda görevleriniz otomatik olarak senkronize edilir.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>Detaylı Analitik</h4>
                        <p>Verimliliğinizi takip edin ve gelişim alanlarınızı keşfedin.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4>Takım Çalışması</h4>
                        <p>Ekibinizle görevleri paylaşın ve birlikte çalışın.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h4>Akıllı Hatırlatmalar</h4>
                        <p>Önemli görevlerinizi kaçırmayın, akıllı bildirimlerle haberdar olun.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>Güvenli Veri Saklama</h4>
                        <p>Verileriniz end-to-end şifreleme ile güvende tutulur.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h4>Takvim Entegrasyonu</h4>
                        <p>Google Calendar, Outlook ve Apple Calendar ile otomatik senkronizasyon.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h4>Zaman Takibi</h4>
                        <p>Görevlerinize harcadığınız zamanı otomatik olarak takip edin ve raporlayın.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-export"></i>
                        </div>
                        <h4>Rapor ve Dışa Aktarma</h4>
                        <p>Detaylı raporlar oluşturun ve PDF, Excel formatlarında dışa aktarın.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h4>Gerçek Zamanlı Mesajlaşma</h4>
                        <p>Takım üyeleriyle anlık mesajlaşma ve dosya paylaşımı yapın.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>Offline Çalışma</h4>
                        <p>İnternet bağlantısı olmadan da görevlerinizi yönetin, senkronizasyon otomatik.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Specifications -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-lg-8 mx-auto">
                    <h2 class="display-5 fw-bold mb-4">Teknik Özellikler</h2>
                    <p class="lead text-muted">TaskMaster Pro'nun güçlü teknik altyapısı</p>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="feature-card">
                        <h5><i class="fas fa-code me-2 text-primary"></i>Geliştirme Teknolojileri</h5>
                        <ul class="list-unstyled mt-3">
                            <li><i class="fas fa-check text-success me-2"></i>React Native 0.72</li>
                            <li><i class="fas fa-check text-success me-2"></i>TypeScript</li>
                            <li><i class="fas fa-check text-success me-2"></i>Redux Toolkit</li>
                            <li><i class="fas fa-check text-success me-2"></i>React Navigation 6</li>
                            <li><i class="fas fa-check text-success me-2"></i>Expo SDK 49</li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="feature-card">
                        <h5><i class="fas fa-server me-2 text-primary"></i>Backend & Database</h5>
                        <ul class="list-unstyled mt-3">
                            <li><i class="fas fa-check text-success me-2"></i>Node.js & Express</li>
                            <li><i class="fas fa-check text-success me-2"></i>MongoDB Atlas</li>
                            <li><i class="fas fa-check text-success me-2"></i>Redis Cache</li>
                            <li><i class="fas fa-check text-success me-2"></i>Socket.io</li>
                            <li><i class="fas fa-check text-success me-2"></i>JWT Authentication</li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="feature-card">
                        <h5><i class="fas fa-cloud me-2 text-primary"></i>Cloud & DevOps</h5>
                        <ul class="list-unstyled mt-3">
                            <li><i class="fas fa-check text-success me-2"></i>AWS EC2 & S3</li>
                            <li><i class="fas fa-check text-success me-2"></i>Docker Containers</li>
                            <li><i class="fas fa-check text-success me-2"></i>CI/CD Pipeline</li>
                            <li><i class="fas fa-check text-success me-2"></i>CloudFlare CDN</li>
                            <li><i class="fas fa-check text-success me-2"></i>Auto Scaling</li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="feature-card">
                        <h5><i class="fas fa-shield-alt me-2 text-primary"></i>Güvenlik & Compliance</h5>
                        <ul class="list-unstyled mt-3">
                            <li><i class="fas fa-check text-success me-2"></i>AES-256 Şifreleme</li>
                            <li><i class="fas fa-check text-success me-2"></i>GDPR Uyumlu</li>
                            <li><i class="fas fa-check text-success me-2"></i>2FA Authentication</li>
                            <li><i class="fas fa-check text-success me-2"></i>SSL/TLS 1.3</li>
                            <li><i class="fas fa-check text-success me-2"></i>SOC 2 Type II</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">500K+</span>
                        <h5>Aktif Kullanıcı</h5>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">2M+</span>
                        <h5>Tamamlanan Görev</h5>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">4.8</span>
                        <h5>App Store Puanı</h5>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">150+</span>
                        <h5>Ülke</h5>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-lg-8 mx-auto">
                    <h2 class="display-5 fw-bold mb-4">Kullanıcı Yorumları</h2>
                    <p class="lead text-muted">TaskMaster Pro kullanıcılarının deneyimleri</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4">
                    <div class="testimonial-card">
                        <div class="rating mb-3">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"TaskMaster Pro sayesinde verimliliğim %40 arttı. Yapay zeka önerileri gerçekten işe yarıyor!"</p>
                        <div class="d-flex align-items-center">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face" 
                                 alt="Ayşe Yılmaz" class="user-avatar me-3">
                            <div>
                                <h6 class="mb-0">Ayşe Yılmaz</h6>
                                <small class="text-muted">Proje Yöneticisi</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="testimonial-card">
                        <div class="rating mb-3">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Ekip çalışması özelliği harika. Tüm takımımız artık daha organize çalışıyor."</p>
                        <div class="d-flex align-items-center">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face" 
                                 alt="Mehmet Kaya" class="user-avatar me-3">
                            <div>
                                <h6 class="mb-0">Mehmet Kaya</h6>
                                <small class="text-muted">Startup Kurucusu</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="testimonial-card">
                        <div class="rating mb-3">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Kullanımı çok kolay ve sezgisel. Günlük rutinlerimi organize etmek hiç bu kadar kolay olmamıştı."</p>
                        <div class="d-flex align-items-center">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face" 
                                 alt="Zeynep Demir" class="user-avatar me-3">
                            <div>
                                <h6 class="mb-0">Zeynep Demir</h6>
                                <small class="text-muted">Freelancer</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-8 mx-auto">
                    <h2 class="display-5 fw-bold mb-4">Hemen İndirin ve Başlayın!</h2>
                    <p class="lead mb-4">TaskMaster Pro ile verimliliğinizi artırın. İlk 30 gün ücretsiz!</p>
                    
                    <div class="d-flex justify-content-center flex-wrap">
                        <button class="btn btn-download">
                            <i class="fab fa-apple me-2"></i>App Store
                        </button>
                        <button class="btn btn-download">
                            <i class="fab fa-google-play me-2"></i>Google Play
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 TaskMaster Pro - Broxy Code tarafından geliştirildi</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="index.html" class="text-white text-decoration-none">
                        <i class="fas fa-arrow-left me-2"></i>Ana Sayfaya Dön
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = counter.textContent;
                const numericTarget = parseInt(target.replace(/[^\d]/g, ''));
                let current = 0;
                const increment = numericTarget / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= numericTarget) {
                        counter.textContent = target;
                        clearInterval(timer);
                    } else {
                        if (target.includes('K')) {
                            counter.textContent = Math.floor(current) + 'K+';
                        } else if (target.includes('M')) {
                            counter.textContent = Math.floor(current) + 'M+';
                        } else if (target.includes('.')) {
                            counter.textContent = (current / 10).toFixed(1);
                        } else {
                            counter.textContent = Math.floor(current) + '+';
                        }
                    }
                }, 20);
            });
        }

        // Trigger counter animation when stats section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(document.querySelector('.stats-section'));
    </script>
</body>
</html>
