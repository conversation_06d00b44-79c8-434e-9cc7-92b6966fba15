{"nav": {"home": "Home", "about": "About", "portfolio": "Portfolio", "contact": "Contact", "language": "Language"}, "hero": {"title": "Open the Doors to the Digital World with Broxy Code", "subtitle": "Professional Web Design & Development", "description": "We develop modern, responsive and SEO-friendly websites. Expert in e-commerce, corporate website and portfolio design. Professional web solutions to make your business stand out in the digital world with React, Node.js, HTML5, CSS3 and JavaScript.", "cta_portfolio": "View My Work", "cta_contact": "Get In Touch"}, "about": {"title": "About Broxy Code", "subtitle": "Turkey's Best Web Development Services - Why Broxy Code?", "founder_name": "<PERSON><PERSON><PERSON><PERSON>", "founder_title": "Founder & Senior Web Developer", "founder_description": "As the founder and senior web developer of Broxy Code, I specialize in e-commerce, corporate website and portfolio projects with React, Node.js, HTML5, CSS3, JavaScript and modern web technologies. I provide professional solutions in web development in Turkey."}, "services": {"responsive": {"title": "Responsive Web Design", "description": "Mobile-friendly, responsive websites that look perfect on all devices. Modern design solutions with HTML5, CSS3 and Bootstrap."}, "seo": {"title": "SEO Optimization & Performance", "description": "Fast-loading, SEO-friendly websites that rank high on Google. Including technical SEO and performance optimization."}, "ecommerce": {"title": "E-commerce & Corporate Solutions", "description": "E-commerce sites, corporate websites and portfolio design. Custom solutions with React, Node.js and modern technologies."}}, "portfolio": {"title": "Portfolio", "subtitle": "Our Latest Projects", "view_demo": "Live Demo", "view_details": "View Details", "ecommerce": {"title": "E-Commerce Website", "description": "Modern e-commerce platform. Full-featured online store solution with payment system, product catalog management and admin panel.", "technologies": "React, Node.js, MongoDB, Stripe", "client": "TechStore", "duration": "8 weeks", "year": "2024"}, "corporate": {"title": "Corporate Website", "description": "Professional corporate website. Platform that strengthens company image with CMS, multi-language support and SEO optimization.", "technologies": "HTML5, CSS3, JavaScript, PHP", "client": "Innovation Technology", "duration": "6 weeks", "year": "2024"}, "restaurant": {"title": "Restaurant Website", "description": "Modern restaurant website. Platform that enhances customer experience with online reservation, digital menu and WhatsApp ordering system.", "technologies": "React, Node.js, WhatsApp API", "client": "Taste Station", "duration": "4 weeks", "year": "2024"}, "portfolio_site": {"title": "Portfolio Website", "description": "Personal portfolio website. Professional personal brand platform with React SPA, blog system and dark/light theme support.", "technologies": "React, Next.js, Tailwind CSS", "client": "Freelance Developer", "duration": "5 weeks", "year": "2024"}}, "contact": {"title": "Contact", "subtitle": "Get in touch with us for your projects", "form": {"name": "Your Full Name", "email": "Your Email Address", "phone": "Your Phone Number", "message": "Your Message", "submit": "Send Message", "sending": "Sending...", "success": "Your message has been sent successfully!", "error": "An error occurred. Please try again."}, "info": {"phone": "+90 552 001 75 38", "email": "<EMAIL>", "address": "Istanbul, Turkey"}}, "footer": {"copyright": "© 2024 Broxy Code - <PERSON><PERSON><PERSON><PERSON>. All rights reserved.", "description": "Professional web design and development services | Web Developer Turkey | E-commerce & Corporate Website"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "close": "Close", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "view": "View", "back": "Back", "next": "Next", "previous": "Previous"}}