/* Broxy Code - Dark Mode Implementation */
/* Complete dark/light theme system with CSS custom properties */

/* Light theme (default) */
:root {
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-dark: #2d3436;
    --bg-card: #ffffff;
    --bg-overlay: rgba(255, 255, 255, 0.95);
    
    /* Text Colors */
    --text-primary: #2d3436;
    --text-secondary: #636e72;
    --text-muted: #b2bec3;
    --text-inverse: #ffffff;
    
    /* Border Colors */
    --border-color: #dee2e6;
    --border-light: #e9ecef;
    --border-dark: #adb5bd;
    
    /* Shadow Colors */
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.15);
    
    /* Component Colors */
    --navbar-bg: rgba(255, 255, 255, 0.95);
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --modal-bg: #ffffff;
    --dropdown-bg: #ffffff;
    
    /* Theme Colors (remain consistent) */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #ff6b35;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* Dark theme */
[data-theme="dark"] {
    /* Background Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3d3d3d;
    --bg-dark: #0d1117;
    --bg-card: #21262d;
    --bg-overlay: rgba(26, 26, 26, 0.95);
    
    /* Text Colors */
    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --text-muted: #6e7681;
    --text-inverse: #1a1a1a;
    
    /* Border Colors */
    --border-color: #30363d;
    --border-light: #21262d;
    --border-dark: #484f58;
    
    /* Shadow Colors */
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-hover: rgba(0, 0, 0, 0.4);
    
    /* Component Colors */
    --navbar-bg: rgba(26, 26, 26, 0.95);
    --card-bg: #21262d;
    --input-bg: #0d1117;
    --input-border: #30363d;
    --modal-bg: #21262d;
    --dropdown-bg: #21262d;
    
    /* Adjust theme colors for dark mode */
    --primary-color: #8fa4f3;
    --secondary-color: #9d7cc0;
    --accent-color: #ff8c5a;
}

/* Base styles using CSS custom properties */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Navigation */
.navbar {
    background-color: var(--navbar-bg) !important;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.navbar-brand,
.nav-link {
    color: var(--text-primary) !important;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-toggler {
    border-color: var(--border-color);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

[data-theme="dark"] .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28240, 246, 252, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Cards */
.card,
.service-card,
.card-enhanced {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.card-body {
    color: var(--text-primary);
}

/* Buttons */
.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

.btn-outline-light {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-outline-light {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-outline-light:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-control:focus {
    background-color: var(--input-bg);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control::placeholder {
    color: var(--text-muted);
}

/* Modals */
.modal-content {
    background-color: var(--modal-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.modal-header {
    border-bottom-color: var(--border-color);
}

.modal-footer {
    border-top-color: var(--border-color);
}

/* Dropdowns */
.dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--border-color);
}

.dropdown-item {
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Alerts */
.alert {
    border-color: var(--border-color);
}

.alert-info {
    background-color: var(--bg-secondary);
    border-color: var(--info-color);
    color: var(--text-primary);
}

/* Tables */
.table {
    color: var(--text-primary);
}

.table-striped > tbody > tr:nth-of-type(odd) > td,
.table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: var(--bg-secondary);
}

/* Badges */
.badge {
    color: var(--text-inverse);
}

/* Progress bars */
.progress {
    background-color: var(--bg-secondary);
}

/* List groups */
.list-group-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* Theme toggle button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 80px;
    z-index: 1000;
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 50px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px var(--shadow-color);
}

.theme-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px var(--shadow-hover);
}

.theme-toggle i {
    color: var(--text-primary);
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

/* Theme transition animations */
.theme-transition {
    transition: all 0.3s ease;
}

/* Scrollbar styling for dark mode */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--border-dark);
}

/* Hero section adjustments */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* Footer */
.footer,
footer {
    background-color: var(--bg-dark) !important;
    color: var(--text-inverse);
}

/* Text colors */
.text-muted {
    color: var(--text-muted) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

/* Background utilities */
.bg-light {
    background-color: var(--bg-secondary) !important;
}

.bg-white {
    background-color: var(--bg-primary) !important;
}

/* Border utilities */
.border {
    border-color: var(--border-color) !important;
}

/* Custom dark mode specific styles */
[data-theme="dark"] .gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

[data-theme="dark"] .glow {
    box-shadow: 0 0 20px rgba(143, 164, 243, 0.3);
}

[data-theme="dark"] .glow:hover {
    box-shadow: 0 0 30px rgba(143, 164, 243, 0.5);
}

/* Animation adjustments for dark mode */
[data-theme="dark"] .card-enhanced:hover::before {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0.1;
}

/* Ensure images work well in dark mode */
[data-theme="dark"] img {
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

[data-theme="dark"] img:hover {
    opacity: 1;
}

/* Code blocks and pre elements */
[data-theme="dark"] pre,
[data-theme="dark"] code {
    background-color: var(--bg-dark);
    color: var(--text-primary);
    border-color: var(--border-color);
}

/* Selection color */
[data-theme="dark"] ::selection {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

/* Focus indicators */
[data-theme="dark"] :focus {
    outline-color: var(--primary-color);
}

/* Smooth transitions for theme switching */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .theme-toggle,
    * {
        transition: none !important;
    }
}
