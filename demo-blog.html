<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechBlog Pro - Modern Blog Platformu | Broxy Code Demo</title>
    <meta name="description" content="TechBlog Pro - Modern blog platformu demo sitesi. Broxy Code tarafından geliştirildi.">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #ff6b35;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #e9ecef;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
        }

        .navbar {
            background: white !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 100px 0 80px;
            position: relative;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        }

        .search-box {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 50px;
            padding: 15px 25px;
            color: white;
            width: 100%;
            max-width: 500px;
        }

        .search-box::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .search-box:focus {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            outline: none;
            color: white;
        }

        .blog-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            height: 100%;
        }

        .blog-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .blog-image {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }

        .blog-content {
            padding: 25px;
        }

        .blog-category {
            background: var(--primary-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 15px;
        }

        .blog-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .blog-excerpt {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .blog-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .author-info {
            display: flex;
            align-items: center;
        }

        .author-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }

        .sidebar h5 {
            color: var(--dark-color);
            margin-bottom: 20px;
            font-weight: 600;
        }

        .category-list {
            list-style: none;
            padding: 0;
        }

        .category-list li {
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .category-list li:last-child {
            border-bottom: none;
        }

        .category-list a {
            color: var(--dark-color);
            text-decoration: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-list a:hover {
            color: var(--primary-color);
        }

        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tag {
            background: var(--light-color);
            color: var(--dark-color);
            padding: 5px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .tag:hover {
            background: var(--primary-color);
            color: white;
        }

        .recent-post {
            display: flex;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .recent-post:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .recent-post-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
            margin-right: 15px;
        }

        .recent-post-content h6 {
            font-size: 0.9rem;
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .recent-post-content small {
            color: #6c757d;
        }

        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 50px;
        }

        .back-to-portfolio {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255,255,255,0.9);
            color: var(--dark-color);
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-to-portfolio:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: var(--dark-color);
        }

        .newsletter-section {
            background: var(--light-color);
            padding: 60px 0;
        }

        .newsletter-form {
            max-width: 500px;
            margin: 0 auto;
        }

        .newsletter-input {
            border-radius: 50px;
            border: 1px solid var(--border-color);
            padding: 15px 25px;
        }

        .newsletter-btn {
            background: var(--primary-color);
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 60px 0 40px;
            }

            .blog-content {
                padding: 20px;
            }

            .sidebar {
                margin-top: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Back to Portfolio Button -->
    <a href="index.html#portfolio" class="back-to-portfolio">
        <i class="fas fa-arrow-left me-2"></i>Portfolio'ya Dön
    </a>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-blog me-2"></i>TechBlog Pro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Ana Sayfa</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#categories">Kategoriler</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">Hakkında</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">İletişim</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-8 mx-auto">
                    <h1 class="display-4 fw-bold mb-4">TechBlog Pro</h1>
                    <p class="lead mb-4">
                        Teknoloji dünyasındaki en son gelişmeleri takip edin.
                        Yazılım, web geliştirme, yapay zeka ve daha fazlası hakkında kaliteli içerikler.
                    </p>
                    <div class="d-flex justify-content-center">
                        <input type="text" class="search-box" placeholder="Blog yazılarında ara...">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Blog Posts -->
                <div class="col-lg-8">
                    <div class="row">
                        <!-- Featured Post -->
                        <div class="col-12 mb-4">
                            <div class="blog-card">
                                <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&w=800&h=400&fit=crop"
                                     alt="React 18 Yenilikleri" class="blog-image">
                                <div class="blog-content">
                                    <span class="blog-category">React</span>
                                    <h2 class="blog-title">React 18'in Getirdiği Yenilikler ve Concurrent Features</h2>
                                    <p class="blog-excerpt">
                                        React 18 ile gelen concurrent rendering, automatic batching ve Suspense
                                        güncellemeleri hakkında detaylı bir inceleme. Modern React uygulamaları
                                        geliştirmek için bilmeniz gerekenler.
                                    </p>
                                    <div class="blog-meta">
                                        <div class="author-info">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face"
                                                 alt="Yılmaz Demircioğlu" class="author-avatar">
                                            <span>Yılmaz Demircioğlu</span>
                                        </div>
                                        <span>15 Mart 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Regular Posts -->
                        <div class="col-md-6 mb-4">
                            <div class="blog-card">
                                <img src="https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&w=400&h=200&fit=crop"
                                     alt="Next.js 14" class="blog-image">
                                <div class="blog-content">
                                    <span class="blog-category">Next.js</span>
                                    <h3 class="blog-title">Next.js 14 ile Server Components</h3>
                                    <p class="blog-excerpt">
                                        Next.js 14'ün server components özelliği ile performanslı web uygulamaları geliştirme rehberi.
                                    </p>
                                    <div class="blog-meta">
                                        <div class="author-info">
                                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face"
                                                 alt="Ayşe Kaya" class="author-avatar">
                                            <span>Ayşe Kaya</span>
                                        </div>
                                        <span>12 Mart 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="blog-card">
                                <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&w=400&h=200&fit=crop"
                                     alt="TypeScript" class="blog-image">
                                <div class="blog-content">
                                    <span class="blog-category">TypeScript</span>
                                    <h3 class="blog-title">TypeScript 5.0 Yenilikleri</h3>
                                    <p class="blog-excerpt">
                                        TypeScript 5.0 ile gelen decorators, const type parameters ve diğer önemli güncellemeler.
                                    </p>
                                    <div class="blog-meta">
                                        <div class="author-info">
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face"
                                                 alt="Mehmet Özkan" class="author-avatar">
                                            <span>Mehmet Özkan</span>
                                        </div>
                                        <span>10 Mart 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="blog-card">
                                <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&w=400&h=200&fit=crop"
                                     alt="Node.js" class="blog-image">
                                <div class="blog-content">
                                    <span class="blog-category">Node.js</span>
                                    <h3 class="blog-title">Node.js Performance Optimizasyonu</h3>
                                    <p class="blog-excerpt">
                                        Node.js uygulamalarında performans artırma teknikleri ve best practices.
                                    </p>
                                    <div class="blog-meta">
                                        <div class="author-info">
                                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face"
                                                 alt="Zeynep Yılmaz" class="author-avatar">
                                            <span>Zeynep Yılmaz</span>
                                        </div>
                                        <span>8 Mart 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="blog-card">
                                <img src="https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&w=400&h=200&fit=crop"
                                     alt="AI" class="blog-image">
                                <div class="blog-content">
                                    <span class="blog-category">Yapay Zeka</span>
                                    <h3 class="blog-title">ChatGPT API ile Web Uygulamaları</h3>
                                    <p class="blog-excerpt">
                                        OpenAI ChatGPT API'sini kullanarak akıllı web uygulamaları geliştirme rehberi.
                                    </p>
                                    <div class="blog-meta">
                                        <div class="author-info">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face"
                                                 alt="Yılmaz Demircioğlu" class="author-avatar">
                                            <span>Yılmaz Demircioğlu</span>
                                        </div>
                                        <span>5 Mart 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="blog-card">
                                <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-4.0.3&w=400&h=200&fit=crop"
                                     alt="Mobile Development" class="blog-image">
                                <div class="blog-content">
                                    <span class="blog-category">Mobile</span>
                                    <h3 class="blog-title">React Native vs Flutter 2024</h3>
                                    <p class="blog-excerpt">
                                        2024'te mobil uygulama geliştirme için React Native ve Flutter karşılaştırması.
                                    </p>
                                    <div class="blog-meta">
                                        <div class="author-info">
                                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&w=60&h=60&fit=crop&crop=face"
                                                 alt="Ayşe Kaya" class="author-avatar">
                                            <span>Ayşe Kaya</span>
                                        </div>
                                        <span>3 Mart 2024</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination-wrapper">
                        <nav>
                            <ul class="pagination">
                                <li class="page-item disabled">
                                    <span class="page-link">Önceki</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">Sonraki</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Categories -->
                    <div class="sidebar">
                        <h5><i class="fas fa-folder me-2"></i>Kategoriler</h5>
                        <ul class="category-list">
                            <li><a href="#">React <span class="badge bg-secondary">12</span></a></li>
                            <li><a href="#">Next.js <span class="badge bg-secondary">8</span></a></li>
                            <li><a href="#">TypeScript <span class="badge bg-secondary">15</span></a></li>
                            <li><a href="#">Node.js <span class="badge bg-secondary">10</span></a></li>
                            <li><a href="#">Yapay Zeka <span class="badge bg-secondary">6</span></a></li>
                            <li><a href="#">Mobile <span class="badge bg-secondary">9</span></a></li>
                            <li><a href="#">DevOps <span class="badge bg-secondary">7</span></a></li>
                        </ul>
                    </div>

                    <!-- Recent Posts -->
                    <div class="sidebar">
                        <h5><i class="fas fa-clock me-2"></i>Son Yazılar</h5>
                        <div class="recent-post">
                            <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&w=60&h=60&fit=crop"
                                 alt="React" class="recent-post-image">
                            <div class="recent-post-content">
                                <h6><a href="#" class="text-decoration-none text-dark">React 18 Concurrent Features</a></h6>
                                <small>15 Mart 2024</small>
                            </div>
                        </div>
                        <div class="recent-post">
                            <img src="https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&w=60&h=60&fit=crop"
                                 alt="Next.js" class="recent-post-image">
                            <div class="recent-post-content">
                                <h6><a href="#" class="text-decoration-none text-dark">Next.js 14 Server Components</a></h6>
                                <small>12 Mart 2024</small>
                            </div>
                        </div>
                        <div class="recent-post">
                            <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&w=60&h=60&fit=crop"
                                 alt="TypeScript" class="recent-post-image">
                            <div class="recent-post-content">
                                <h6><a href="#" class="text-decoration-none text-dark">TypeScript 5.0 Yenilikleri</a></h6>
                                <small>10 Mart 2024</small>
                            </div>
                        </div>
                    </div>

                    <!-- Tags -->
                    <div class="sidebar">
                        <h5><i class="fas fa-tags me-2"></i>Etiketler</h5>
                        <div class="tag-cloud">
                            <a href="#" class="tag">JavaScript</a>
                            <a href="#" class="tag">React</a>
                            <a href="#" class="tag">Next.js</a>
                            <a href="#" class="tag">TypeScript</a>
                            <a href="#" class="tag">Node.js</a>
                            <a href="#" class="tag">MongoDB</a>
                            <a href="#" class="tag">Express</a>
                            <a href="#" class="tag">API</a>
                            <a href="#" class="tag">Frontend</a>
                            <a href="#" class="tag">Backend</a>
                            <a href="#" class="tag">Full Stack</a>
                            <a href="#" class="tag">Web Development</a>
                        </div>
                    </div>

                    <!-- Newsletter -->
                    <div class="sidebar">
                        <h5><i class="fas fa-envelope me-2"></i>Newsletter</h5>
                        <p>En son yazılarımızdan haberdar olmak için e-posta listemize katılın.</p>
                        <form class="newsletter-form">
                            <div class="mb-3">
                                <input type="email" class="form-control newsletter-input" placeholder="E-posta adresiniz">
                            </div>
                            <button type="submit" class="btn newsletter-btn w-100">
                                <i class="fas fa-paper-plane me-2"></i>Abone Ol
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-8 mx-auto">
                    <h2 class="h3 mb-4">Haftalık Newsletter</h2>
                    <p class="mb-4">Teknoloji dünyasındaki en son gelişmeleri ve blog yazılarımızı e-posta ile alın.</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control newsletter-input" placeholder="E-posta adresiniz">
                            <button class="btn newsletter-btn" type="submit">
                                <i class="fas fa-paper-plane me-2"></i>Abone Ol
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><i class="fas fa-blog me-2"></i>TechBlog Pro</h5>
                    <p>Teknoloji dünyasındaki en son gelişmeleri takip edin. Kaliteli içerikler ve uzman görüşleri.</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-github"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Kategoriler</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-white-50">React</a></li>
                        <li><a href="#" class="text-white-50">Next.js</a></li>
                        <li><a href="#" class="text-white-50">TypeScript</a></li>
                        <li><a href="#" class="text-white-50">Node.js</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Sayfalar</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-white-50">Ana Sayfa</a></li>
                        <li><a href="#" class="text-white-50">Hakkında</a></li>
                        <li><a href="#" class="text-white-50">İletişim</a></li>
                        <li><a href="#" class="text-white-50">Gizlilik</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6>İletişim</h6>
                    <p class="text-white-50">
                        <i class="fas fa-envelope me-2"></i><EMAIL><br>
                        <i class="fas fa-phone me-2"></i>+90 552 001 75 38<br>
                        <i class="fas fa-map-marker-alt me-2"></i>İstanbul, Türkiye
                    </p>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 TechBlog Pro - Broxy Code tarafından geliştirildi</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="index.html" class="text-white text-decoration-none">
                        <i class="fas fa-arrow-left me-2"></i>Ana Sayfaya Dön
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Search functionality
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const blogCards = document.querySelectorAll('.blog-card');

            blogCards.forEach(card => {
                const title = card.querySelector('.blog-title').textContent.toLowerCase();
                const excerpt = card.querySelector('.blog-excerpt').textContent.toLowerCase();
                const category = card.querySelector('.blog-category').textContent.toLowerCase();

                if (title.includes(searchTerm) || excerpt.includes(searchTerm) || category.includes(searchTerm)) {
                    card.closest('.col-md-6, .col-12').style.display = 'block';
                } else {
                    card.closest('.col-md-6, .col-12').style.display = 'none';
                }
            });
        });

        // Newsletter form
        document.querySelectorAll('.newsletter-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = this.querySelector('input[type="email"]').value;
                if (email) {
                    alert('Teşekkürler! Newsletter listemize eklendi: ' + email);
                    this.querySelector('input[type="email"]').value = '';
                }
            });
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Blog card hover effects
        document.querySelectorAll('.blog-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.cursor = 'pointer';
            });

            card.addEventListener('click', function() {
                const title = this.querySelector('.blog-title').textContent;
                alert('Blog yazısı açılıyor: ' + title);
            });
        });
    </script>
</body>
</html>