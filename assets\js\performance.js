// Broxy Code - Performance Optimized JavaScript
// Modern animations and performance enhancements

class PerformanceOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupScrollAnimations();
        this.setupParallaxEffects();
        this.setupAsyncLoading();
        this.optimizeImages();
    }

    // Lazy loading for images
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for older browsers
            document.querySelectorAll('img[data-src]').forEach(img => {
                img.src = img.dataset.src;
                img.classList.add('loaded');
            });
        }
    }

    // Scroll-triggered animations
    setupScrollAnimations() {
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animated');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            document.querySelectorAll('.animate-on-scroll, .animate-fade-in, .animate-slide-left, .animate-slide-right, .animate-scale').forEach(el => {
                animationObserver.observe(el);
            });
        }
    }

    // Parallax effects with performance optimization
    setupParallaxEffects() {
        if (window.innerWidth > 768 && !window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            let ticking = false;

            const updateParallax = () => {
                const scrolled = window.pageYOffset;
                const parallaxElements = document.querySelectorAll('.parallax-element');
                
                parallaxElements.forEach(element => {
                    const speed = element.dataset.speed || 0.5;
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translate3d(0, ${yPos}px, 0)`;
                });

                ticking = false;
            };

            const requestParallaxUpdate = () => {
                if (!ticking) {
                    requestAnimationFrame(updateParallax);
                    ticking = true;
                }
            };

            window.addEventListener('scroll', requestParallaxUpdate, { passive: true });
        }
    }

    // Async loading for non-critical scripts
    setupAsyncLoading() {
        const loadScript = (src, callback) => {
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            script.onload = callback;
            document.head.appendChild(script);
        };

        // Load non-critical scripts after page load
        window.addEventListener('load', () => {
            // Load analytics or other non-critical scripts here
            setTimeout(() => {
                this.loadNonCriticalResources();
            }, 1000);
        });
    }

    loadNonCriticalResources() {
        // Load additional CSS files
        const loadCSS = (href) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            document.head.appendChild(link);
        };

        // Load performance CSS
        loadCSS('assets/css/performance.css');
    }

    // Image optimization
    optimizeImages() {
        // Convert images to WebP if supported
        const supportsWebP = () => {
            const canvas = document.createElement('canvas');
            return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        };

        if (supportsWebP()) {
            document.querySelectorAll('img[data-webp]').forEach(img => {
                img.src = img.dataset.webp;
            });
        }
    }
}

// Modern Visual Effects Manager
class VisualEffectsManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupHoverEffects();
        this.setupTextAnimations();
        this.setupButtonEffects();
        this.setupCardAnimations();
    }

    setupHoverEffects() {
        // Enhanced button hover effects
        document.querySelectorAll('.btn-enhanced').forEach(btn => {
            btn.addEventListener('mouseenter', (e) => {
                this.createRippleEffect(e);
            });
        });

        // Card hover effects
        document.querySelectorAll('.card-enhanced').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    createRippleEffect(e) {
        const button = e.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    setupTextAnimations() {
        // Gradient text animation
        document.querySelectorAll('.gradient-text-animated').forEach(text => {
            text.addEventListener('mouseenter', () => {
                text.style.animationDuration = '1s';
            });

            text.addEventListener('mouseleave', () => {
                text.style.animationDuration = '4s';
            });
        });

        // Text reveal animation
        this.setupTextReveal();
    }

    setupTextReveal() {
        const textElements = document.querySelectorAll('.text-reveal');
        
        if ('IntersectionObserver' in window) {
            const textObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'reveal 1.5s ease forwards';
                    }
                });
            });

            textElements.forEach(el => textObserver.observe(el));
        }
    }

    setupButtonEffects() {
        document.querySelectorAll('.btn-enhanced').forEach(btn => {
            // Add loading state
            btn.addEventListener('click', (e) => {
                if (btn.classList.contains('btn-loading')) return;
                
                btn.classList.add('btn-loading');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                
                setTimeout(() => {
                    btn.classList.remove('btn-loading');
                    btn.innerHTML = originalText;
                }, 2000);
            });
        });
    }

    setupCardAnimations() {
        // Staggered animation for cards
        const cards = document.querySelectorAll('.card-enhanced');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }
}

// Performance monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }

    init() {
        this.measurePageLoad();
        this.measureResourceTiming();
        this.setupPerformanceObserver();
    }

    measurePageLoad() {
        window.addEventListener('load', () => {
            const perfData = performance.getEntriesByType('navigation')[0];
            this.metrics.pageLoad = {
                domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                totalTime: perfData.loadEventEnd - perfData.fetchStart
            };
            
            console.log('Page Load Metrics:', this.metrics.pageLoad);
        });
    }

    measureResourceTiming() {
        window.addEventListener('load', () => {
            const resources = performance.getEntriesByType('resource');
            this.metrics.resources = resources.map(resource => ({
                name: resource.name,
                duration: resource.duration,
                size: resource.transferSize
            }));
            
            console.log('Resource Timing:', this.metrics.resources);
        });
    }

    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // Largest Contentful Paint
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.lcp = lastEntry.startTime;
                console.log('LCP:', this.metrics.lcp);
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

            // First Input Delay
            const fidObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    this.metrics.fid = entry.processingStart - entry.startTime;
                    console.log('FID:', this.metrics.fid);
                });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
        }
    }
}

// Initialize all performance optimizations
document.addEventListener('DOMContentLoaded', () => {
    new PerformanceOptimizer();
    new VisualEffectsManager();
    new PerformanceMonitor();
});

// CSS for ripple effect
const rippleCSS = `
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

// Inject ripple CSS
const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
