const { MongoClient } = require('mongodb');

let cachedClient = null;

async function connectToDatabase() {
    if (cachedClient) {
        return cachedClient;
    }

    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URL;

    if (!mongoUri) {
        throw new Error('MongoDB URI not found in environment variables. Please set MONGODB_URI or MONGO_URL.');
    }

    const client = new MongoClient(mongoUri);
    await client.connect();
    cachedClient = client;
    return client;
}

const portfolioProjects = [
    {
        title: "Modern E-Ticaret Sitesi",
        description: "Responsive tasarım, ödeme sistemi entegrasyonu, admin paneli ve stok yönetimi ile tam özellikli e-ticaret platformu.",
        technologies: ["HTML5", "CSS3", "JavaScript", "Bootstrap", "PHP", "MySQL"],
        imageUrl: "https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80",
        projectUrl: "https://demo-ecommerce.broxycode.com",
        features: [
            "Responsive mobil uyumlu tasarım",
            "Güvenli ödeme sistemi entegrasyonu",
            "Ürün katalog yönetimi",
            "Kullanıcı hesap sistemi",
            "Sipariş takip sistemi",
            "Admin dashboard",
            "SEO optimizasyonu"
        ],
        client: "TechStore A.Ş.",
        duration: "6 hafta",
        year: "2024"
    },
    {
        title: "Kurumsal Website",
        description: "SEO optimizasyonu, hızlı yükleme, modern tasarım ve içerik yönetim sistemi ile profesyonel kurumsal web sitesi.",
        technologies: ["HTML5", "CSS3", "jQuery", "Bootstrap", "WordPress"],
        imageUrl: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80",
        projectUrl: "https://demo-corporate.broxycode.com",
        features: [
            "Modern ve profesyonel tasarım",
            "İçerik yönetim sistemi (CMS)",
            "Çoklu dil desteği",
            "İletişim formu entegrasyonu",
            "Google Analytics entegrasyonu",
            "Sosyal medya entegrasyonu",
            "Hızlı yükleme optimizasyonu"
        ],
        client: "İnovasyon Teknoloji Ltd.",
        duration: "4 hafta",
        year: "2024"
    },
    {
        title: "Restaurant Website",
        description: "Online rezervasyon sistemi, menü yönetimi, mobil uyumlu tasarım ve sosyal medya entegrasyonu ile restaurant web sitesi.",
        technologies: ["HTML5", "CSS3", "PHP", "MySQL", "JavaScript"],
        imageUrl: "https://images.unsplash.com/photo-1555421689-491a97ff2040?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80",
        projectUrl: "https://demo-restaurant.broxycode.com",
        features: [
            "Online rezervasyon sistemi",
            "Dijital menü yönetimi",
            "Galeri ve fotoğraf showcase",
            "Konum ve harita entegrasyonu",
            "Sosyal medya entegrasyonu",
            "Mobil uyumlu responsive tasarım",
            "WhatsApp sipariş entegrasyonu"
        ],
        client: "Lezzet Durağı Restaurant",
        duration: "3 hafta",
        year: "2024"
    },
    {
        title: "Kişisel Portfolio",
        description: "Modern tasarım, animasyonlar, admin paneli, blog sistemi ve proje showcase ile kişisel portfolio web sitesi.",
        technologies: ["React", "Node.js", "MongoDB", "Express", "CSS3"],
        imageUrl: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80",
        projectUrl: "https://demo-portfolio.broxycode.com",
        features: [
            "Modern React tabanlı SPA",
            "Dinamik proje showcase",
            "Blog yazma sistemi",
            "Admin panel ile içerik yönetimi",
            "İletişim formu ve mesajlaşma",
            "Smooth animasyonlar",
            "Dark/Light tema desteği"
        ],
        client: "Freelance Developer",
        duration: "5 hafta",
        year: "2024"
    }
];

module.exports = async (req, res) => {
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method === 'POST') {
        try {
            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const collection = db.collection('portfolio');

            // Check if projects already exist
            const existingCount = await collection.countDocuments();

            if (existingCount > 0) {
                return res.status(200).json({
                    success: true,
                    message: 'Portfolio projeleri zaten mevcut.',
                    count: existingCount
                });
            }

            // Add timestamps to projects
            const projectsWithTimestamps = portfolioProjects.map(project => ({
                ...project,
                createdAt: new Date(),
                updatedAt: new Date()
            }));

            // Insert all projects
            const result = await collection.insertMany(projectsWithTimestamps);

            res.status(200).json({
                success: true,
                message: `${result.insertedCount} portfolio projesi başarıyla eklendi!`,
                insertedIds: result.insertedIds
            });

        } catch (error) {
            console.error('Init portfolio error:', error);
            res.status(500).json({
                success: false,
                error: 'Sunucu hatası.'
            });
        }
    } else {
        res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }
};