<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Ticaret Demo | TechStore - Broxy Code</title>
    <meta name="description" content="Modern e-ticaret sitesi demo. Ödeme sistemi, ür<PERSON>n katalog yönetimi ve admin paneli örneği.">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Poppins', sans-serif; }
        .demo-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem 0; }
        .product-card { transition: transform 0.3s ease; border: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .product-card:hover { transform: translateY(-5px); box-shadow: 0 8px 15px rgba(0,0,0,0.2); }
        .price { font-size: 1.5rem; font-weight: 600; color: #28a745; }
        .cart-btn { background: #007bff; border: none; padding: 10px 20px; border-radius: 25px; }
        .cart-btn:hover { background: #0056b3; }
        .demo-badge { position: fixed; top: 20px; right: 20px; z-index: 1000; }
        .category-nav { background: #f8f9fa; padding: 1rem 0; }
        .search-bar { max-width: 500px; margin: 0 auto; }
    </style>
</head>
<body>
    <!-- Demo Badge -->
    <div class="demo-badge">
        <span class="badge bg-warning text-dark fs-6">
            <i class="fas fa-eye me-1"></i>DEMO
        </span>
    </div>

    <!-- Header -->
    <div class="demo-header text-center">
        <div class="container">
            <h1><i class="fas fa-laptop me-2"></i>TechStore</h1>
            <p class="lead">Modern E-Ticaret Sitesi Demo</p>
            <p class="mb-0">Broxy Code tarafından geliştirilmiştir</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-store me-2"></i>TechStore</a>
            <div class="search-bar">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Ürün ara...">
                    <button class="btn btn-outline-primary"><i class="fas fa-search"></i></button>
                </div>
            </div>
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-primary me-2">
                    <i class="fas fa-user me-1"></i>Giriş
                </button>
                <button class="btn btn-primary position-relative">
                    <i class="fas fa-shopping-cart me-1"></i>Sepet
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">3</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Categories -->
    <div class="category-nav">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-2 col-6 mb-2">
                    <a href="#" class="text-decoration-none text-dark">
                        <i class="fas fa-laptop fa-2x d-block mb-1"></i>
                        <small>Laptop</small>
                    </a>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <a href="#" class="text-decoration-none text-dark">
                        <i class="fas fa-mobile-alt fa-2x d-block mb-1"></i>
                        <small>Telefon</small>
                    </a>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <a href="#" class="text-decoration-none text-dark">
                        <i class="fas fa-tablet-alt fa-2x d-block mb-1"></i>
                        <small>Tablet</small>
                    </a>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <a href="#" class="text-decoration-none text-dark">
                        <i class="fas fa-headphones fa-2x d-block mb-1"></i>
                        <small>Aksesuar</small>
                    </a>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <a href="#" class="text-decoration-none text-dark">
                        <i class="fas fa-gamepad fa-2x d-block mb-1"></i>
                        <small>Gaming</small>
                    </a>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <a href="#" class="text-decoration-none text-dark">
                        <i class="fas fa-tv fa-2x d-block mb-1"></i>
                        <small>TV & Ses</small>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Products -->
    <div class="container my-5">
        <h2 class="text-center mb-4">Öne Çıkan Ürünler</h2>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card product-card h-100">
                    <img src="https://images.unsplash.com/photo-1496181133206-80ce9b88a853?ixlib=rb-4.0.3&w=300&h=200&fit=crop" class="card-img-top" alt="Laptop">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">MacBook Pro 14"</h5>
                        <p class="card-text text-muted">M2 Pro çip, 16GB RAM, 512GB SSD</p>
                        <div class="mt-auto">
                            <div class="price mb-2">₺45.999</div>
                            <button class="btn cart-btn w-100 text-white">
                                <i class="fas fa-cart-plus me-2"></i>Sepete Ekle
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card product-card h-100">
                    <img src="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&w=300&h=200&fit=crop" class="card-img-top" alt="Phone">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">iPhone 15 Pro</h5>
                        <p class="card-text text-muted">128GB, Titanium Blue</p>
                        <div class="mt-auto">
                            <div class="price mb-2">₺52.999</div>
                            <button class="btn cart-btn w-100 text-white">
                                <i class="fas fa-cart-plus me-2"></i>Sepete Ekle
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card product-card h-100">
                    <img src="https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&w=300&h=200&fit=crop" class="card-img-top" alt="Headphones">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">AirPods Pro</h5>
                        <p class="card-text text-muted">Aktif Gürültü Engelleme</p>
                        <div class="mt-auto">
                            <div class="price mb-2">₺8.999</div>
                            <button class="btn cart-btn w-100 text-white">
                                <i class="fas fa-cart-plus me-2"></i>Sepete Ekle
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card product-card h-100">
                    <img src="https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?ixlib=rb-4.0.3&w=300&h=200&fit=crop" class="card-img-top" alt="Watch">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">Apple Watch Series 9</h5>
                        <p class="card-text text-muted">45mm, GPS + Cellular</p>
                        <div class="mt-auto">
                            <div class="price mb-2">₺15.999</div>
                            <button class="btn cart-btn w-100 text-white">
                                <i class="fas fa-cart-plus me-2"></i>Sepete Ekle
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-3 mb-3">
                    <i class="fas fa-shipping-fast fa-3x text-primary mb-3"></i>
                    <h5>Hızlı Kargo</h5>
                    <p class="text-muted">Aynı gün teslimat</p>
                </div>
                <div class="col-md-3 mb-3">
                    <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                    <h5>Güvenli Ödeme</h5>
                    <p class="text-muted">SSL sertifikalı</p>
                </div>
                <div class="col-md-3 mb-3">
                    <i class="fas fa-undo fa-3x text-warning mb-3"></i>
                    <h5>Kolay İade</h5>
                    <p class="text-muted">14 gün iade garantisi</p>
                </div>
                <div class="col-md-3 mb-3">
                    <i class="fas fa-headset fa-3x text-info mb-3"></i>
                    <h5>7/24 Destek</h5>
                    <p class="text-muted">Müşteri hizmetleri</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Portfolio -->
    <div class="text-center py-4">
        <a href="index.html" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Portfolio'ya Dön
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple cart functionality
        document.querySelectorAll('.cart-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-check me-2"></i>Eklendi!';
                this.classList.remove('cart-btn');
                this.classList.add('btn-success');
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Sepete Ekle';
                    this.classList.remove('btn-success');
                    this.classList.add('cart-btn');
                }, 2000);
            });
        });
    </script>
</body>
</html>
