<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Broxy Code - Admin Panel</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .main-content {
            margin-left: 250px;
            padding: 20px;
        }

        .admin-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .portfolio-item-admin {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .contact-message {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-4">
            <h4 class="text-center mb-4">
                <i class="fas fa-code me-2"></i>Broxy Code Admin
            </h4>
        </div>

        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('portfolio')">
                    <i class="fas fa-briefcase me-2"></i>Portfolio Yönetimi
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('messages')">
                    <i class="fas fa-envelope me-2"></i>Mesajlar
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('settings')">
                    <i class="fas fa-cog me-2"></i>Ayarlar
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="showSection('analytics')">
                    <i class="fas fa-chart-bar me-2"></i>Analitik
                </a>
            </li>
            <li class="nav-item mt-4">
                <a class="nav-link" href="index.html">
                    <i class="fas fa-arrow-left me-2"></i>Ana Siteye Dön
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="admin-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0">Hoş Geldiniz, Yılmaz Demircioğlu</h2>
                    <p class="text-muted mb-0">Broxy Code Admin Paneli</p>
                </div>
                <div class="col-auto">
                    <span class="badge bg-success">Online</span>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard" class="content-section active">
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0">4</h3>
                                <p class="text-muted mb-0">Portfolio Projesi</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0">12</h3>
                                <p class="text-muted mb-0">Yeni Mesaj</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0">1,234</h3>
                                <p class="text-muted mb-0">Site Ziyareti</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-info">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0">89</h3>
                                <p class="text-muted mb-0">Potansiyel Müşteri</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="stats-card">
                        <h5 class="mb-3">Site Trafiği</h5>
                        <canvas id="trafficChart" height="100"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h5 class="mb-3">Son Aktiviteler</h5>
                        <div class="activity-item mb-3">
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <small>Yeni mesaj alındı - 2 saat önce</small>
                        </div>
                        <div class="activity-item mb-3">
                            <i class="fas fa-briefcase text-success me-2"></i>
                            <small>Portfolio güncellendi - 1 gün önce</small>
                        </div>
                        <div class="activity-item mb-3">
                            <i class="fas fa-user text-info me-2"></i>
                            <small>Yeni ziyaretçi - 3 saat önce</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Portfolio Management Section -->
        <div id="portfolio" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>Portfolio Yönetimi</h3>
                <button class="btn btn-primary" onclick="addNewProject()">
                    <i class="fas fa-plus me-2"></i>Yeni Proje Ekle
                </button>
            </div>

            <div id="portfolioList">
                <div class="portfolio-item-admin">
                    <div class="row">
                        <div class="col-md-3">
                            <img src="https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200&q=80"
                                 class="img-fluid rounded" alt="E-Ticaret Sitesi">
                        </div>
                        <div class="col-md-6">
                            <h5>Modern E-Ticaret Sitesi</h5>
                            <p class="text-muted">Responsive tasarım, ödeme sistemi entegrasyonu</p>
                            <span class="badge bg-primary me-1">HTML5</span>
                            <span class="badge bg-primary me-1">CSS3</span>
                            <span class="badge bg-primary">JavaScript</span>
                        </div>
                        <div class="col-md-3 text-end">
                            <button class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="portfolio-item-admin">
                    <div class="row">
                        <div class="col-md-3">
                            <img src="https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200&q=80"
                                 class="img-fluid rounded" alt="Kurumsal Website">
                        </div>
                        <div class="col-md-6">
                            <h5>Kurumsal Website</h5>
                            <p class="text-muted">SEO optimizasyonu, hızlı yükleme, modern tasarım</p>
                            <span class="badge bg-success me-1">HTML5</span>
                            <span class="badge bg-success me-1">CSS3</span>
                            <span class="badge bg-success">jQuery</span>
                        </div>
                        <div class="col-md-3 text-end">
                            <button class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages Section -->
        <div id="messages" class="content-section">
            <h3 class="mb-4">İletişim Mesajları</h3>

            <div class="contact-message">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">Ahmet Yılmaz</h6>
                    <small class="text-muted">2 saat önce</small>
                </div>
                <p class="text-muted mb-2"><EMAIL> | 0532 123 45 67</p>
                <p class="mb-3">Merhaba, e-ticaret sitesi için fiyat teklifi alabilir miyim?</p>
                <div>
                    <button class="btn btn-sm btn-primary me-2">Yanıtla</button>
                    <button class="btn btn-sm btn-outline-secondary">Arşivle</button>
                </div>
            </div>

            <div class="contact-message">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">Fatma Demir</h6>
                    <small class="text-muted">1 gün önce</small>
                </div>
                <p class="text-muted mb-2"><EMAIL> | 0533 987 65 43</p>
                <p class="mb-3">Kurumsal website projesi için görüşmek istiyoruz.</p>
                <div>
                    <button class="btn btn-sm btn-primary me-2">Yanıtla</button>
                    <button class="btn btn-sm btn-outline-secondary">Arşivle</button>
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div id="settings" class="content-section">
            <h3 class="mb-4">Ayarlar</h3>

            <div class="row">
                <div class="col-md-6">
                    <div class="stats-card">
                        <h5 class="mb-3">Şirket Bilgileri</h5>
                        <form>
                            <div class="mb-3">
                                <label class="form-label">Şirket Adı</label>
                                <input type="text" class="form-control" value="Broxy Code">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">E-posta</label>
                                <input type="email" class="form-control" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Telefon</label>
                                <input type="tel" class="form-control" value="05520017538">
                            </div>
                            <button type="submit" class="btn btn-primary">Güncelle</button>
                        </form>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="stats-card">
                        <h5 class="mb-3">Kişisel Bilgiler</h5>
                        <form>
                            <div class="mb-3">
                                <label class="form-label">Ad Soyad</label>
                                <input type="text" class="form-control" value="Yılmaz Demircioğlu">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Pozisyon</label>
                                <input type="text" class="form-control" value="Kurucu & Baş Geliştirici">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Hakkında</label>
                                <textarea class="form-control" rows="3">Broxy Code'un kurucusu ve baş geliştiricisi olarak, modern web teknolojileri ile işletmenizi dijital dünyada öne çıkarıyorum.</textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Güncelle</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Section -->
        <div id="analytics" class="content-section">
            <h3 class="mb-4">Analitik</h3>

            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="stats-card">
                        <h5 class="mb-3">Ziyaretçi İstatistikleri</h5>
                        <canvas id="visitorChart" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="stats-card">
                        <h5 class="mb-3">Popüler Sayfalar</h5>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Ana Sayfa</span>
                                <span class="text-primary">45%</span>
                            </div>
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" style="width: 45%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Portfolio</span>
                                <span class="text-primary">30%</span>
                            </div>
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" style="width: 30%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>İletişim</span>
                                <span class="text-primary">25%</span>
                            </div>
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" style="width: 25%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav links
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked nav link
            event.target.classList.add('active');
        }

        // Portfolio management functions
        function addNewProject() {
            const title = prompt('Proje başlığını girin:');
            if (title) {
                const description = prompt('Proje açıklamasını girin:');
                const technologies = prompt('Kullanılan teknolojileri girin (virgülle ayırın):');

                if (description && technologies) {
                    const portfolioList = document.getElementById('portfolioList');
                    const newProject = document.createElement('div');
                    newProject.className = 'portfolio-item-admin';
                    newProject.innerHTML = `
                        <div class="row">
                            <div class="col-md-3">
                                <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=Yeni+Proje"
                                     class="img-fluid rounded" alt="${title}">
                            </div>
                            <div class="col-md-6">
                                <h5>${title}</h5>
                                <p class="text-muted">${description}</p>
                                ${technologies.split(',').map(tech => `<span class="badge bg-primary me-1">${tech.trim()}</span>`).join('')}
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-sm btn-outline-primary me-2" onclick="editProject(this)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProject(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    portfolioList.appendChild(newProject);

                    // Update stats
                    updateStats();

                    alert('Yeni proje başarıyla eklendi!');
                }
            }
        }

        function editProject(button) {
            const projectDiv = button.closest('.portfolio-item-admin');
            const title = projectDiv.querySelector('h5').textContent;
            const description = projectDiv.querySelector('p').textContent;

            const newTitle = prompt('Yeni başlık:', title);
            if (newTitle) {
                const newDescription = prompt('Yeni açıklama:', description);
                if (newDescription) {
                    projectDiv.querySelector('h5').textContent = newTitle;
                    projectDiv.querySelector('p').textContent = newDescription;
                    alert('Proje başarıyla güncellendi!');
                }
            }
        }

        function deleteProject(button) {
            if (confirm('Bu projeyi silmek istediğinizden emin misiniz?')) {
                button.closest('.portfolio-item-admin').remove();
                updateStats();
                alert('Proje başarıyla silindi!');
            }
        }

        function updateStats() {
            const projectCount = document.querySelectorAll('.portfolio-item-admin').length;
            document.querySelector('.stats-card h3').textContent = projectCount;
        }

        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Traffic Chart
            const trafficCtx = document.getElementById('trafficChart').getContext('2d');
            new Chart(trafficCtx, {
                type: 'line',
                data: {
                    labels: ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran'],
                    datasets: [{
                        label: 'Ziyaretçi Sayısı',
                        data: [120, 190, 300, 500, 200, 300],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Visitor Chart
            const visitorCtx = document.getElementById('visitorChart').getContext('2d');
            new Chart(visitorCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Masaüstü', 'Mobil', 'Tablet'],
                    datasets: [{
                        data: [60, 30, 10],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        });

        // Form submissions
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Ayarlar başarıyla güncellendi!');
            });
        });

        console.log('Broxy Code Admin Panel loaded successfully!');
    </script>
</body>
</html>