<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Demo | Creative Developer - Broxy Code</title>
    <meta name="description" content="Modern portfolio website demo. React SPA, blog sistemi ve dark/light tema desteğ<PERSON>.">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --bg-color: #ffffff;
            --text-color: #2d3436;
            --card-bg: #ffffff;
        }
        
        [data-theme="dark"] {
            --primary-color: #a29bfe;
            --secondary-color: #6c5ce7;
            --bg-color: #2d3436;
            --text-color: #ddd;
            --card-bg: #636e72;
        }
        
        body { 
            font-family: 'Poppins', sans-serif; 
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }
        
        .hero-section { 
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white; 
            padding: 8rem 0; 
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        
        .demo-badge { position: fixed; top: 20px; right: 20px; z-index: 1000; }
        .theme-toggle { position: fixed; top: 20px; left: 20px; z-index: 1000; }
        
        .project-card { 
            background: var(--card-bg);
            border: none; 
            border-radius: 20px; 
            overflow: hidden; 
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .project-card:hover { 
            transform: translateY(-10px); 
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .skill-badge { 
            background: var(--primary-color); 
            color: white; 
            padding: 8px 16px; 
            border-radius: 25px; 
            margin: 5px;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .skill-badge:hover {
            transform: scale(1.1);
            background: var(--secondary-color);
        }
        
        .blog-card {
            background: var(--card-bg);
            border: none;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .blog-card:hover {
            transform: translateY(-5px);
        }
        
        .btn-portfolio { 
            background: var(--primary-color); 
            border: none; 
            padding: 12px 30px; 
            border-radius: 25px; 
            transition: all 0.3s ease;
        }
        
        .btn-portfolio:hover { 
            background: var(--secondary-color); 
            transform: translateY(-2px);
        }
        
        .navbar {
            background: var(--card-bg) !important;
            backdrop-filter: blur(10px);
        }
        
        .typing-animation {
            border-right: 2px solid white;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { border-color: white; }
            51%, 100% { border-color: transparent; }
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <!-- Demo Badge -->
    <div class="demo-badge">
        <span class="badge bg-warning text-dark fs-6">
            <i class="fas fa-eye me-1"></i>DEMO
        </span>
    </div>

    <!-- Theme Toggle -->
    <div class="theme-toggle">
        <button class="btn btn-outline-light" id="themeToggle">
            <i class="fas fa-moon" id="themeIcon"></i>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#" style="color: var(--primary-color);">
                <i class="fas fa-code me-2"></i>Alex Johnson
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Ana Sayfa</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">Hakkımda</a></li>
                    <li class="nav-item"><a class="nav-link" href="#projects">Projeler</a></li>
                    <li class="nav-item"><a class="nav-link" href="#blog">Blog</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">İletişim</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section text-center position-relative">
        <div class="floating-shapes">
            <div class="shape" style="top: 10%; left: 10%; font-size: 2rem;">💻</div>
            <div class="shape" style="top: 20%; right: 10%; font-size: 1.5rem;">🚀</div>
            <div class="shape" style="bottom: 20%; left: 20%; font-size: 2rem;">⚡</div>
            <div class="shape" style="bottom: 10%; right: 20%; font-size: 1.5rem;">🎨</div>
        </div>
        <div class="container position-relative">
            <div class="row align-items-center">
                <div class="col-lg-6 text-lg-start">
                    <h1 class="display-4 fw-bold mb-3">
                        Merhaba, Ben <span style="color: #ffeaa7;">Alex</span>
                    </h1>
                    <h2 class="h3 mb-4">
                        <span id="typingText" class="typing-animation">Full Stack Developer</span>
                    </h2>
                    <p class="lead mb-4">
                        React, Node.js ve modern web teknolojileri ile yaratıcı dijital deneyimler tasarlıyorum.
                    </p>
                    <button class="btn btn-portfolio btn-lg text-white me-3" onclick="scrollToSection('projects')">
                        <i class="fas fa-eye me-2"></i>Projelerimi Gör
                    </button>
                    <button class="btn btn-outline-light btn-lg" onclick="scrollToSection('contact')">
                        <i class="fas fa-envelope me-2"></i>İletişime Geç
                    </button>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&w=400&h=400&fit=crop" 
                             class="img-fluid rounded-circle shadow-lg" 
                             alt="Alex Johnson" 
                             style="width: 300px; height: 300px; object-fit: cover;">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-4">
                    <h2 class="display-5 fw-bold mb-4" style="color: var(--primary-color);">Hakkımda</h2>
                    <p class="lead">5+ yıllık deneyime sahip tutkulu bir full stack developer'ım.</p>
                    <p>Modern web teknolojileri ile kullanıcı deneyimini ön planda tutan, performanslı ve ölçeklenebilir uygulamalar geliştiriyorum. Her projede yenilikçi çözümler üretmeyi ve en son teknolojileri kullanmayı seviyorum.</p>
                    
                    <h4 class="mt-4 mb-3">Yeteneklerim</h4>
                    <div class="skills-container">
                        <span class="skill-badge">React</span>
                        <span class="skill-badge">Node.js</span>
                        <span class="skill-badge">TypeScript</span>
                        <span class="skill-badge">MongoDB</span>
                        <span class="skill-badge">Express.js</span>
                        <span class="skill-badge">Next.js</span>
                        <span class="skill-badge">GraphQL</span>
                        <span class="skill-badge">Docker</span>
                        <span class="skill-badge">AWS</span>
                        <span class="skill-badge">Git</span>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 rounded" style="background: var(--card-bg);">
                                <h3 style="color: var(--primary-color);">50+</h3>
                                <p>Tamamlanan Proje</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 rounded" style="background: var(--card-bg);">
                                <h3 style="color: var(--primary-color);">5+</h3>
                                <p>Yıl Deneyim</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 rounded" style="background: var(--card-bg);">
                                <h3 style="color: var(--primary-color);">30+</h3>
                                <p>Mutlu Müşteri</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 rounded" style="background: var(--card-bg);">
                                <h3 style="color: var(--primary-color);">24/7</h3>
                                <p>Destek</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-5" style="background: var(--bg-color);">
        <div class="container">
            <h2 class="text-center display-5 fw-bold mb-5" style="color: var(--primary-color);">Son Projelerim</h2>
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="project-card card h-100">
                        <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&w=400&h=250&fit=crop" class="card-img-top" alt="E-commerce App">
                        <div class="card-body">
                            <h5 class="card-title">E-Commerce Platform</h5>
                            <p class="card-text">React ve Node.js ile geliştirilmiş modern e-ticaret platformu. Stripe ödeme entegrasyonu ve admin paneli.</p>
                            <div class="mb-3">
                                <span class="badge bg-primary me-1">React</span>
                                <span class="badge bg-success me-1">Node.js</span>
                                <span class="badge bg-warning">MongoDB</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-github me-1"></i>GitHub
                                </a>
                                <a href="#" class="btn btn-primary btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>Demo
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="project-card card h-100">
                        <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-4.0.3&w=400&h=250&fit=crop" class="card-img-top" alt="Task Manager">
                        <div class="card-body">
                            <h5 class="card-title">Task Management App</h5>
                            <p class="card-text">Real-time işbirliği özellikli proje yönetim uygulaması. Socket.io ile canlı güncellemeler.</p>
                            <div class="mb-3">
                                <span class="badge bg-info me-1">Vue.js</span>
                                <span class="badge bg-dark me-1">Express</span>
                                <span class="badge bg-danger">Socket.io</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-github me-1"></i>GitHub
                                </a>
                                <a href="#" class="btn btn-primary btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>Demo
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="project-card card h-100">
                        <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&w=400&h=250&fit=crop" class="card-img-top" alt="Analytics Dashboard">
                        <div class="card-body">
                            <h5 class="card-title">Analytics Dashboard</h5>
                            <p class="card-text">Gerçek zamanlı veri görselleştirme dashboard'u. Chart.js ve D3.js ile interaktif grafikler.</p>
                            <div class="mb-3">
                                <span class="badge bg-secondary me-1">Next.js</span>
                                <span class="badge bg-primary me-1">TypeScript</span>
                                <span class="badge bg-success">PostgreSQL</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-github me-1"></i>GitHub
                                </a>
                                <a href="#" class="btn btn-primary btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>Demo
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section id="blog" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center display-5 fw-bold mb-5" style="color: var(--primary-color);">Blog Yazılarım</h2>
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="blog-card card h-100">
                        <img src="https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&w=400&h=200&fit=crop" class="card-img-top" alt="React">
                        <div class="card-body">
                            <span class="badge bg-primary mb-2">React</span>
                            <h5 class="card-title">React 18'in Yeni Özellikleri</h5>
                            <p class="card-text">React 18 ile gelen Concurrent Features, Suspense ve diğer yenilikler hakkında detaylı inceleme.</p>
                            <small class="text-muted">15 Aralık 2024</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="blog-card card h-100">
                        <img src="https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&w=400&h=200&fit=crop" class="card-img-top" alt="Node.js">
                        <div class="card-body">
                            <span class="badge bg-success mb-2">Node.js</span>
                            <h5 class="card-title">Node.js Performance Optimizasyonu</h5>
                            <p class="card-text">Node.js uygulamalarında performans artırma teknikleri ve best practice'ler.</p>
                            <small class="text-muted">10 Aralık 2024</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="blog-card card h-100">
                        <img src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&w=400&h=200&fit=crop" class="card-img-top" alt="JavaScript">
                        <div class="card-body">
                            <span class="badge bg-warning mb-2">JavaScript</span>
                            <h5 class="card-title">Modern JavaScript ES2024</h5>
                            <p class="card-text">JavaScript'in en son özelliklerini keşfedin ve projelerinizde nasıl kullanacağınızı öğrenin.</p>
                            <small class="text-muted">5 Aralık 2024</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
        <div class="container">
            <div class="row justify-content-center text-center text-white">
                <div class="col-lg-8">
                    <h2 class="display-5 fw-bold mb-4">Birlikte Çalışalım</h2>
                    <p class="lead mb-5">Projeniz için benimle iletişime geçin. Hayalinizdeki uygulamayı birlikte gerçeğe dönüştürelim.</p>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="p-3">
                                <i class="fas fa-envelope fa-2x mb-3"></i>
                                <h5>E-posta</h5>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="p-3">
                                <i class="fas fa-phone fa-2x mb-3"></i>
                                <h5>Telefon</h5>
                                <p>+90 532 123 45 67</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="p-3">
                                <i class="fas fa-map-marker-alt fa-2x mb-3"></i>
                                <h5>Konum</h5>
                                <p>İstanbul, Türkiye</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <a href="#" class="btn btn-outline-light btn-lg me-3">
                            <i class="fab fa-github me-2"></i>GitHub
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg me-3">
                            <i class="fab fa-linkedin me-2"></i>LinkedIn
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg">
                            <i class="fab fa-twitter me-2"></i>Twitter
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Back to Portfolio -->
    <div class="text-center py-4" style="background: var(--bg-color);">
        <a href="index.html" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Portfolio'ya Dön
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const body = document.body;

        themeToggle.addEventListener('click', () => {
            if (body.getAttribute('data-theme') === 'dark') {
                body.removeAttribute('data-theme');
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'dark');
            }
        });

        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            body.setAttribute('data-theme', 'dark');
            themeIcon.className = 'fas fa-sun';
        }

        // Typing animation
        const texts = ['Full Stack Developer', 'React Specialist', 'Node.js Expert', 'UI/UX Enthusiast'];
        let textIndex = 0;
        let charIndex = 0;
        const typingElement = document.getElementById('typingText');

        function typeText() {
            if (charIndex < texts[textIndex].length) {
                typingElement.textContent = texts[textIndex].substring(0, charIndex + 1);
                charIndex++;
                setTimeout(typeText, 100);
            } else {
                setTimeout(eraseText, 2000);
            }
        }

        function eraseText() {
            if (charIndex > 0) {
                typingElement.textContent = texts[textIndex].substring(0, charIndex - 1);
                charIndex--;
                setTimeout(eraseText, 50);
            } else {
                textIndex = (textIndex + 1) % texts.length;
                setTimeout(typeText, 500);
            }
        }

        // Start typing animation
        typeText();

        // Smooth scrolling
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({ behavior: 'smooth' });
        }

        // Smooth scrolling for nav links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });
    </script>
</body>
</html>
