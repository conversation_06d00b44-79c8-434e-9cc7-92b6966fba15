const { MongoClient } = require('mongodb');
const validator = require('validator');

let cachedClient = null;

async function connectToDatabase() {
    if (cachedClient) {
        return cachedClient;
    }

    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URL;

    if (!mongoUri) {
        throw new Error('MongoDB URI not found in environment variables. Please set MONGODB_URI or MONGO_URL.');
    }

    const client = new MongoClient(mongoUri);
    await client.connect();
    cachedClient = client;
    return client;
}

module.exports = async (req, res) => {
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method === 'POST') {
        try {
            const { name, email, phone, message } = req.body;

            // Validation
            if (!name || !email || !message) {
                return res.status(400).json({
                    success: false,
                    error: 'Ad, e-posta ve mesaj alanları zorunludur.'
                });
            }

            if (!validator.isEmail(email)) {
                return res.status(400).json({
                    success: false,
                    error: 'Geçerli bir e-posta adresi girin.'
                });
            }

            // Connect to database
            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const collection = db.collection('contacts');

            // Save contact message
            const contactData = {
                name: validator.escape(name),
                email: validator.normalizeEmail(email),
                phone: phone ? validator.escape(phone) : '',
                message: validator.escape(message),
                createdAt: new Date(),
                ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
                userAgent: req.headers['user-agent'],
                status: 'new'
            };

            const result = await collection.insertOne(contactData);

            // Track analytics
            const analyticsCollection = db.collection('analytics');
            await analyticsCollection.insertOne({
                type: 'contact_form_submission',
                timestamp: new Date(),
                ip: contactData.ip,
                userAgent: contactData.userAgent
            });

            res.status(200).json({
                success: true,
                message: 'Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.',
                id: result.insertedId
            });

        } catch (error) {
            console.error('Contact form error:', error);
            res.status(500).json({
                success: false,
                error: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.'
            });
        }
    } else if (req.method === 'GET') {
        try {
            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const collection = db.collection('contacts');

            const contacts = await collection
                .find({})
                .sort({ createdAt: -1 })
                .limit(50)
                .toArray();

            res.status(200).json({
                success: true,
                contacts: contacts
            });

        } catch (error) {
            console.error('Get contacts error:', error);
            res.status(500).json({
                success: false,
                error: 'Sunucu hatası.'
            });
        }
    } else {
        res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }
};