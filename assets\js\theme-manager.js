// Broxy Code - Dark Mode Theme Manager
// Complete dark/light theme system with system preference detection

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.systemPreference = 'light';
        this.storageKey = 'broxycode_theme';
        this.init();
    }

    init() {
        this.detectSystemPreference();
        this.loadSavedTheme();
        this.createThemeToggle();
        this.setupEventListeners();
        this.applyTheme();
        this.setupSystemPreferenceListener();
    }

    // Detect system color scheme preference
    detectSystemPreference() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            this.systemPreference = 'dark';
        } else {
            this.systemPreference = 'light';
        }
    }

    // Load saved theme from localStorage
    loadSavedTheme() {
        const savedTheme = localStorage.getItem(this.storageKey);
        
        if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
            this.currentTheme = savedTheme;
        } else {
            // Default to auto (follow system preference)
            this.currentTheme = 'auto';
        }
    }

    // Get effective theme (resolve 'auto' to actual theme)
    getEffectiveTheme() {
        if (this.currentTheme === 'auto') {
            return this.systemPreference;
        }
        return this.currentTheme;
    }

    // Apply theme to document
    applyTheme() {
        const effectiveTheme = this.getEffectiveTheme();
        
        // Remove existing theme classes
        document.documentElement.removeAttribute('data-theme');
        document.body.classList.remove('theme-light', 'theme-dark');
        
        // Apply new theme
        if (effectiveTheme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.body.classList.add('theme-dark');
        } else {
            document.documentElement.setAttribute('data-theme', 'light');
            document.body.classList.add('theme-light');
        }

        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(effectiveTheme);
        
        // Update theme toggle button
        this.updateThemeToggle();
        
        // Trigger theme change event
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { 
                theme: this.currentTheme,
                effectiveTheme: effectiveTheme
            }
        }));
    }

    // Update meta theme-color for mobile browsers
    updateMetaThemeColor(theme) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        
        if (theme === 'dark') {
            metaThemeColor.content = '#1a1a1a';
        } else {
            metaThemeColor.content = '#ffffff';
        }
    }

    // Create theme toggle button
    createThemeToggle() {
        // Check if toggle already exists
        if (document.getElementById('themeToggle')) {
            return;
        }

        const themeToggle = document.createElement('button');
        themeToggle.id = 'themeToggle';
        themeToggle.className = 'theme-toggle';
        themeToggle.setAttribute('aria-label', 'Toggle theme');
        themeToggle.setAttribute('title', 'Toggle dark/light theme');
        
        // Create icon
        const icon = document.createElement('i');
        icon.id = 'themeIcon';
        themeToggle.appendChild(icon);
        
        // Add to page
        document.body.appendChild(themeToggle);
        
        return themeToggle;
    }

    // Update theme toggle button appearance
    updateThemeToggle() {
        const themeIcon = document.getElementById('themeIcon');
        const themeToggle = document.getElementById('themeToggle');
        
        if (!themeIcon || !themeToggle) return;
        
        const effectiveTheme = this.getEffectiveTheme();
        
        // Update icon based on current theme
        themeIcon.className = '';
        
        if (this.currentTheme === 'auto') {
            themeIcon.className = 'fas fa-adjust';
            themeToggle.setAttribute('title', `Auto (${effectiveTheme})`);
        } else if (effectiveTheme === 'dark') {
            themeIcon.className = 'fas fa-sun';
            themeToggle.setAttribute('title', 'Switch to light theme');
        } else {
            themeIcon.className = 'fas fa-moon';
            themeToggle.setAttribute('title', 'Switch to dark theme');
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Theme toggle button click
        document.addEventListener('click', (e) => {
            if (e.target.closest('#themeToggle')) {
                this.toggleTheme();
            }
        });

        // Keyboard support for theme toggle
        document.addEventListener('keydown', (e) => {
            if (e.target.closest('#themeToggle') && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                this.toggleTheme();
            }
        });

        // Listen for storage changes (sync across tabs)
        window.addEventListener('storage', (e) => {
            if (e.key === this.storageKey) {
                this.currentTheme = e.newValue || 'auto';
                this.applyTheme();
            }
        });
    }

    // Setup system preference change listener
    setupSystemPreferenceListener() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                this.systemPreference = e.matches ? 'dark' : 'light';
                
                // If current theme is auto, reapply theme
                if (this.currentTheme === 'auto') {
                    this.applyTheme();
                }
            });
        }
    }

    // Toggle theme
    toggleTheme() {
        // Cycle through: light -> dark -> auto -> light
        switch (this.currentTheme) {
            case 'light':
                this.setTheme('dark');
                break;
            case 'dark':
                this.setTheme('auto');
                break;
            case 'auto':
                this.setTheme('light');
                break;
            default:
                this.setTheme('light');
        }
    }

    // Set specific theme
    setTheme(theme) {
        if (!['light', 'dark', 'auto'].includes(theme)) {
            console.error(`Invalid theme: ${theme}`);
            return;
        }

        this.currentTheme = theme;
        localStorage.setItem(this.storageKey, theme);
        this.applyTheme();
        
        // Add visual feedback
        this.showThemeChangeAnimation();
    }

    // Show theme change animation
    showThemeChangeAnimation() {
        const themeToggle = document.getElementById('themeToggle');
        if (!themeToggle) return;

        // Add animation class
        themeToggle.style.transform = 'scale(1.2) rotate(180deg)';
        
        setTimeout(() => {
            themeToggle.style.transform = 'scale(1) rotate(0deg)';
        }, 300);
    }

    // Get current theme
    getCurrentTheme() {
        return this.currentTheme;
    }

    // Get effective theme
    getCurrentEffectiveTheme() {
        return this.getEffectiveTheme();
    }

    // Check if dark mode is active
    isDarkMode() {
        return this.getEffectiveTheme() === 'dark';
    }

    // Force theme (useful for testing)
    forceTheme(theme) {
        if (!['light', 'dark'].includes(theme)) {
            console.error(`Invalid theme for forcing: ${theme}`);
            return;
        }

        const oldTheme = this.currentTheme;
        this.currentTheme = theme;
        this.applyTheme();
        
        // Return function to restore original theme
        return () => {
            this.currentTheme = oldTheme;
            this.applyTheme();
        };
    }

    // Add theme-aware CSS classes to elements
    addThemeAwareClasses() {
        // Add theme-aware classes to common elements
        const elements = {
            '.navbar': 'navbar-theme',
            '.card': 'card-theme',
            '.btn': 'btn-theme',
            '.form-control': 'form-theme'
        };

        Object.entries(elements).forEach(([selector, className]) => {
            document.querySelectorAll(selector).forEach(el => {
                el.classList.add(className);
            });
        });
    }

    // Initialize theme-aware animations
    initThemeAwareAnimations() {
        // Smooth transitions for theme changes
        const style = document.createElement('style');
        style.textContent = `
            .theme-transition * {
                transition: background-color 0.3s ease, 
                           color 0.3s ease, 
                           border-color 0.3s ease,
                           box-shadow 0.3s ease !important;
            }
        `;
        document.head.appendChild(style);
        
        // Add transition class to body
        document.body.classList.add('theme-transition');
    }

    // Handle images in dark mode
    handleDarkModeImages() {
        if (this.isDarkMode()) {
            document.querySelectorAll('img').forEach(img => {
                if (!img.classList.contains('no-dark-filter')) {
                    img.style.filter = 'brightness(0.9)';
                }
            });
        } else {
            document.querySelectorAll('img').forEach(img => {
                img.style.filter = '';
            });
        }
    }

    // Update favicon for dark mode
    updateFavicon() {
        const favicon = document.querySelector('link[rel="icon"]');
        if (!favicon) return;

        const isDark = this.isDarkMode();
        const faviconPath = isDark ? 'assets/favicon-dark.ico' : 'assets/favicon-light.ico';
        
        // Only update if different favicon exists
        const testImg = new Image();
        testImg.onload = () => {
            favicon.href = faviconPath;
        };
        testImg.src = faviconPath;
    }
}

// Theme-aware utility functions
class ThemeUtils {
    static getThemeAwareColor(lightColor, darkColor) {
        const themeManager = window.themeManager;
        if (themeManager && themeManager.isDarkMode()) {
            return darkColor;
        }
        return lightColor;
    }

    static addThemeAwareStyle(element, lightStyles, darkStyles) {
        const themeManager = window.themeManager;
        const styles = (themeManager && themeManager.isDarkMode()) ? darkStyles : lightStyles;
        
        Object.assign(element.style, styles);
    }

    static createThemeAwareElement(tag, lightClass, darkClass) {
        const element = document.createElement(tag);
        const themeManager = window.themeManager;
        
        if (themeManager && themeManager.isDarkMode()) {
            element.className = darkClass;
        } else {
            element.className = lightClass;
        }
        
        return element;
    }
}

// Initialize theme manager
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
    window.ThemeUtils = ThemeUtils;
    
    // Initialize theme-aware features
    window.themeManager.addThemeAwareClasses();
    window.themeManager.initThemeAwareAnimations();
    
    // Listen for theme changes to update images and favicon
    window.addEventListener('themeChanged', () => {
        window.themeManager.handleDarkModeImages();
        window.themeManager.updateFavicon();
    });
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ThemeManager, ThemeUtils };
}
