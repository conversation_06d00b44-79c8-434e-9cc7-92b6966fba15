const { MongoClient, ObjectId } = require('mongodb');
const validator = require('validator');

let cachedClient = null;

async function connectToDatabase() {
    if (cachedClient) {
        return cachedClient;
    }

    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URL;

    if (!mongoUri) {
        throw new Error('MongoDB URI not found in environment variables. Please set MONGODB_URI or MONGO_URL.');
    }

    const client = new MongoClient(mongoUri);
    await client.connect();
    cachedClient = client;
    return client;
}

module.exports = async (req, res) => {
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    try {
        const client = await connectToDatabase();
        const db = client.db('broxycode');
        const collection = db.collection('portfolio');

        if (req.method === 'GET') {
            const portfolioItems = await collection
                .find({})
                .sort({ createdAt: -1 })
                .toArray();

            res.status(200).json({
                success: true,
                portfolio: portfolioItems
            });

        } else if (req.method === 'POST') {
            const { title, description, technologies, imageUrl, projectUrl } = req.body;

            if (!title || !description) {
                return res.status(400).json({
                    success: false,
                    error: 'Başlık ve açıklama zorunludur.'
                });
            }

            const portfolioItem = {
                title: validator.escape(title),
                description: validator.escape(description),
                technologies: technologies ? technologies.map(tech => validator.escape(tech)) : [],
                imageUrl: imageUrl || 'https://via.placeholder.com/600x400/007bff/ffffff?text=Yeni+Proje',
                projectUrl: projectUrl || '',
                createdAt: new Date(),
                updatedAt: new Date()
            };

            const result = await collection.insertOne(portfolioItem);

            res.status(201).json({
                success: true,
                message: 'Portfolio projesi başarıyla eklendi.',
                id: result.insertedId
            });

        } else if (req.method === 'PUT') {
            const { id, title, description, technologies, imageUrl, projectUrl } = req.body;

            if (!id || !ObjectId.isValid(id)) {
                return res.status(400).json({
                    success: false,
                    error: 'Geçerli bir ID gereklidir.'
                });
            }

            const updateData = {
                updatedAt: new Date()
            };

            if (title) updateData.title = validator.escape(title);
            if (description) updateData.description = validator.escape(description);
            if (technologies) updateData.technologies = technologies.map(tech => validator.escape(tech));
            if (imageUrl) updateData.imageUrl = imageUrl;
            if (projectUrl) updateData.projectUrl = projectUrl;

            const result = await collection.updateOne(
                { _id: new ObjectId(id) },
                { $set: updateData }
            );

            if (result.matchedCount === 0) {
                return res.status(404).json({
                    success: false,
                    error: 'Proje bulunamadı.'
                });
            }

            res.status(200).json({
                success: true,
                message: 'Portfolio projesi başarıyla güncellendi.'
            });

        } else if (req.method === 'DELETE') {
            const { id } = req.body;

            if (!id || !ObjectId.isValid(id)) {
                return res.status(400).json({
                    success: false,
                    error: 'Geçerli bir ID gereklidir.'
                });
            }

            const result = await collection.deleteOne({ _id: new ObjectId(id) });

            if (result.deletedCount === 0) {
                return res.status(404).json({
                    success: false,
                    error: 'Proje bulunamadı.'
                });
            }

            res.status(200).json({
                success: true,
                message: 'Portfolio projesi başarıyla silindi.'
            });

        } else {
            res.status(405).json({
                success: false,
                error: 'Method not allowed'
            });
        }

    } catch (error) {
        console.error('Portfolio API error:', error);
        res.status(500).json({
            success: false,
            error: 'Sunucu hatası.'
        });
    }
};