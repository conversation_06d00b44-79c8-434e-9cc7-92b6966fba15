const { MongoClient } = require('mongodb');

let cachedClient = null;

async function connectToDatabase() {
    if (cachedClient) {
        return cachedClient;
    }

    const mongoUri = process.env.MONGODB_URI;
    
    if (!mongoUri) {
        throw new Error('MongoDB URI not found in environment variables');
    }

    const client = new MongoClient(mongoUri);
    await client.connect();
    cachedClient = client;
    return client;
}

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    };

    // <PERSON>le preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    if (event.httpMethod === 'POST') {
        try {
            const { name, email, phone, message } = JSON.parse(event.body);

            // Validation
            if (!name || !email || !message) {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({
                        success: false,
                        error: 'Name, email, and message are required'
                    }),
                };
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({
                        success: false,
                        error: 'Invalid email format'
                    }),
                };
            }

            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const collection = db.collection('contacts');

            const contactData = {
                name: name.trim(),
                email: email.trim().toLowerCase(),
                phone: phone ? phone.trim() : null,
                message: message.trim(),
                ip: event.headers['x-forwarded-for'] || event.headers['x-real-ip'] || 'unknown',
                userAgent: event.headers['user-agent'] || 'unknown',
                createdAt: new Date(),
                status: 'new'
            };

            const result = await collection.insertOne(contactData);

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Message sent successfully',
                    id: result.insertedId
                }),
            };

        } catch (error) {
            console.error('Contact form error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Server error occurred'
                }),
            };
        }
    }

    if (event.httpMethod === 'GET') {
        try {
            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const collection = db.collection('contacts');

            const contacts = await collection
                .find({})
                .sort({ createdAt: -1 })
                .limit(50)
                .toArray();

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    contacts: contacts
                }),
            };

        } catch (error) {
            console.error('Get contacts error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Server error occurred'
                }),
            };
        }
    }

    return {
        statusCode: 405,
        headers,
        body: JSON.stringify({
            success: false,
            error: 'Method not allowed'
        }),
    };
};
