<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restaurant Demo | Lezzet Durağı - Broxy Code</title>
    <meta name="description" content="Modern restaurant website demo. Online rezervasyon, dijital menü ve WhatsApp sipariş sistemi örneği.">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Poppins', sans-serif; }
        .hero-section { 
            background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), 
                        url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3') center/cover; 
            color: white; padding: 8rem 0; min-height: 100vh; display: flex; align-items: center;
        }
        .demo-badge { position: fixed; top: 20px; right: 20px; z-index: 1000; }
        .menu-card { border: none; border-radius: 15px; overflow: hidden; transition: transform 0.3s ease; }
        .menu-card:hover { transform: translateY(-5px); }
        .price-tag { background: #ff6b35; color: white; padding: 5px 15px; border-radius: 20px; font-weight: 600; }
        .reservation-form { background: rgba(255,255,255,0.95); border-radius: 20px; padding: 2rem; }
        .btn-restaurant { background: #ff6b35; border: none; padding: 12px 30px; border-radius: 25px; }
        .btn-restaurant:hover { background: #e55a2b; }
        .social-float { position: fixed; right: 20px; bottom: 20px; z-index: 1000; }
        .whatsapp-btn { background: #25d366; color: white; border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; text-decoration: none; box-shadow: 0 4px 12px rgba(0,0,0,0.3); }
    </style>
</head>
<body>
    <!-- Demo Badge -->
    <div class="demo-badge">
        <span class="badge bg-warning text-dark fs-6">
            <i class="fas fa-eye me-1"></i>DEMO
        </span>
    </div>

    <!-- WhatsApp Float Button -->
    <div class="social-float">
        <a href="https://wa.me/905520017538?text=Merhaba, rezervasyon yapmak istiyorum" class="whatsapp-btn" target="_blank">
            <i class="fab fa-whatsapp fa-2x"></i>
        </a>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-utensils me-2"></i>Lezzet Durağı</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Ana Sayfa</a></li>
                    <li class="nav-item"><a class="nav-link" href="#menu">Menü</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">Hakkımızda</a></li>
                    <li class="nav-item"><a class="nav-link" href="#gallery">Galeri</a></li>
                    <li class="nav-item"><a class="nav-link" href="#reservation">Rezervasyon</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">İletişim</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section text-center">
        <div class="container">
            <h1 class="display-3 fw-bold mb-4">Lezzet Durağı</h1>
            <p class="lead mb-4">Geleneksel Türk mutfağının en lezzetli durağı</p>
            <p class="h5 mb-5">Ailenizle unutulmaz anlar yaşayın</p>
            <button class="btn btn-restaurant btn-lg text-white me-3" onclick="scrollToSection('menu')">
                <i class="fas fa-book-open me-2"></i>Menüyü İncele
            </button>
            <button class="btn btn-outline-light btn-lg" onclick="scrollToSection('reservation')">
                <i class="fas fa-calendar-alt me-2"></i>Rezervasyon Yap
            </button>
        </div>
    </section>

    <!-- Menu Section -->
    <section id="menu" class="py-5">
        <div class="container">
            <h2 class="text-center display-5 fw-bold mb-5">Menümüz</h2>
            
            <!-- Menu Categories -->
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <button class="btn btn-outline-primary me-2 mb-2 menu-filter active" data-filter="all">Tümü</button>
                    <button class="btn btn-outline-primary me-2 mb-2 menu-filter" data-filter="appetizer">Mezeler</button>
                    <button class="btn btn-outline-primary me-2 mb-2 menu-filter" data-filter="main">Ana Yemekler</button>
                    <button class="btn btn-outline-primary me-2 mb-2 menu-filter" data-filter="dessert">Tatlılar</button>
                    <button class="btn btn-outline-primary me-2 mb-2 menu-filter" data-filter="drink">İçecekler</button>
                </div>
            </div>

            <div class="row" id="menuItems">
                <!-- Appetizers -->
                <div class="col-lg-6 mb-4 menu-item" data-category="appetizer">
                    <div class="menu-card card shadow">
                        <div class="row g-0">
                            <div class="col-4">
                                <img src="https://images.unsplash.com/photo-1541014741259-de529411b96a?ixlib=rb-4.0.3&w=200&h=150&fit=crop" class="img-fluid h-100 object-fit-cover" alt="Humus">
                            </div>
                            <div class="col-8">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="card-title">Ev Yapımı Humus</h5>
                                        <span class="price-tag">₺45</span>
                                    </div>
                                    <p class="card-text text-muted">Nohut, tahin, zeytinyağı ve baharatlarla hazırlanan geleneksel humus</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4 menu-item" data-category="appetizer">
                    <div class="menu-card card shadow">
                        <div class="row g-0">
                            <div class="col-4">
                                <img src="https://images.unsplash.com/photo-1599487488170-d11ec9c172f0?ixlib=rb-4.0.3&w=200&h=150&fit=crop" class="img-fluid h-100 object-fit-cover" alt="Çiğ Köfte">
                            </div>
                            <div class="col-8">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="card-title">Çiğ Köfte</h5>
                                        <span class="price-tag">₺35</span>
                                    </div>
                                    <p class="card-text text-muted">Bulgur, domates salçası ve özel baharatlarla hazırlanan lezzetli çiğ köfte</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Courses -->
                <div class="col-lg-6 mb-4 menu-item" data-category="main">
                    <div class="menu-card card shadow">
                        <div class="row g-0">
                            <div class="col-4">
                                <img src="https://images.unsplash.com/photo-1574484284002-952d92456975?ixlib=rb-4.0.3&w=200&h=150&fit=crop" class="img-fluid h-100 object-fit-cover" alt="Kebab">
                            </div>
                            <div class="col-8">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="card-title">Adana Kebap</h5>
                                        <span class="price-tag">₺120</span>
                                    </div>
                                    <p class="card-text text-muted">Özel baharatlarla marine edilmiş dana eti, közde pişirilmiş</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4 menu-item" data-category="main">
                    <div class="menu-card card shadow">
                        <div class="row g-0">
                            <div class="col-4">
                                <img src="https://images.unsplash.com/photo-1551782450-17144efb9c50?ixlib=rb-4.0.3&w=200&h=150&fit=crop" class="img-fluid h-100 object-fit-cover" alt="Döner">
                            </div>
                            <div class="col-8">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="card-title">Tavuk Döner</h5>
                                        <span class="price-tag">₺85</span>
                                    </div>
                                    <p class="card-text text-muted">Taze tavuk eti, özel soslar ve garnitürlerle servis edilir</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Desserts -->
                <div class="col-lg-6 mb-4 menu-item" data-category="dessert">
                    <div class="menu-card card shadow">
                        <div class="row g-0">
                            <div class="col-4">
                                <img src="https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&w=200&h=150&fit=crop" class="img-fluid h-100 object-fit-cover" alt="Baklava">
                            </div>
                            <div class="col-8">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="card-title">Fıstıklı Baklava</h5>
                                        <span class="price-tag">₺65</span>
                                    </div>
                                    <p class="card-text text-muted">El açması yufka, Antep fıstığı ve özel şerbetli geleneksel baklava</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Drinks -->
                <div class="col-lg-6 mb-4 menu-item" data-category="drink">
                    <div class="menu-card card shadow">
                        <div class="row g-0">
                            <div class="col-4">
                                <img src="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?ixlib=rb-4.0.3&w=200&h=150&fit=crop" class="img-fluid h-100 object-fit-cover" alt="Çay">
                            </div>
                            <div class="col-8">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="card-title">Türk Çayı</h5>
                                        <span class="price-tag">₺15</span>
                                    </div>
                                    <p class="card-text text-muted">Geleneksel demlik çayı, taze demlenen özel karışım</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-4">
                    <h2 class="display-5 fw-bold mb-4">Hikayemiz</h2>
                    <p class="lead">1985 yılından bu yana Türk mutfağının en lezzetli tariflerini sizlerle buluşturuyoruz.</p>
                    <p>Geleneksel lezzetleri modern sunum teknikleriyle harmanlayarak, misafirlerimize unutulmaz bir deneyim sunuyoruz. Taze malzemeler, özenle seçilmiş baharatlar ve deneyimli şeflerimizin mahareti ile her tabakta aşkı hissedebilirsiniz.</p>
                    <div class="row mt-4">
                        <div class="col-6">
                            <h4 class="text-primary">38+</h4>
                            <p>Yıllık Deneyim</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">50K+</h4>
                            <p>Mutlu Müşteri</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <img src="https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&w=600&h=400&fit=crop" class="img-fluid rounded shadow" alt="Restaurant Interior">
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="py-5">
        <div class="container">
            <h2 class="text-center display-5 fw-bold mb-5">Galeri</h2>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&w=400&h=300&fit=crop" class="img-fluid rounded shadow" alt="Restaurant">
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&w=400&h=300&fit=crop" class="img-fluid rounded shadow" alt="Food">
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <img src="https://images.unsplash.com/photo-1424847651672-bf20a4b0982b?ixlib=rb-4.0.3&w=400&h=300&fit=crop" class="img-fluid rounded shadow" alt="Interior">
                </div>
            </div>
        </div>
    </section>

    <!-- Reservation Section -->
    <section id="reservation" class="py-5" style="background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3') center/cover;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="reservation-form">
                        <h2 class="text-center mb-4">Rezervasyon Yapın</h2>
                        <form id="reservationForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <input type="text" class="form-control" placeholder="Adınız Soyadınız" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="tel" class="form-control" placeholder="Telefon Numaranız" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="date" class="form-control" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <select class="form-control" required>
                                        <option value="">Saat Seçin</option>
                                        <option value="12:00">12:00</option>
                                        <option value="13:00">13:00</option>
                                        <option value="14:00">14:00</option>
                                        <option value="19:00">19:00</option>
                                        <option value="20:00">20:00</option>
                                        <option value="21:00">21:00</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <select class="form-control" required>
                                        <option value="">Kişi Sayısı</option>
                                        <option value="1">1 Kişi</option>
                                        <option value="2">2 Kişi</option>
                                        <option value="3">3 Kişi</option>
                                        <option value="4">4 Kişi</option>
                                        <option value="5+">5+ Kişi</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="email" class="form-control" placeholder="E-posta Adresiniz">
                                </div>
                                <div class="col-12 mb-3">
                                    <textarea class="form-control" rows="3" placeholder="Özel İstekleriniz (Opsiyonel)"></textarea>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-restaurant btn-lg text-white">
                                        <i class="fas fa-calendar-check me-2"></i>Rezervasyon Yap
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5 bg-dark text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h3><i class="fas fa-map-marker-alt me-2"></i>Adres</h3>
                    <p>Bağdat Caddesi No:123<br>Kadıköy/İstanbul</p>
                </div>
                <div class="col-lg-4 mb-4">
                    <h3><i class="fas fa-phone me-2"></i>Telefon</h3>
                    <p>+90 216 123 45 67<br>+90 532 123 45 67</p>
                </div>
                <div class="col-lg-4 mb-4">
                    <h3><i class="fas fa-clock me-2"></i>Çalışma Saatleri</h3>
                    <p>Pazartesi - Pazar<br>11:00 - 23:00</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Back to Portfolio -->
    <div class="text-center py-4 bg-light">
        <a href="index.html" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Portfolio'ya Dön
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Menu filter functionality
        document.querySelectorAll('.menu-filter').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.menu-filter').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                document.querySelectorAll('.menu-item').forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // Smooth scrolling
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({ behavior: 'smooth' });
        }

        // Reservation form
        document.getElementById('reservationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Demo modunda - Rezervasyon başarıyla alındı! WhatsApp üzerinden onay mesajı gönderilecek.');
        });
    </script>
</body>
</html>
