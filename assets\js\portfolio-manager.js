// Broxy Code - Enhanced Portfolio System
// Advanced filtering, animations, and project management

class PortfolioManager {
    constructor() {
        this.projects = [];
        this.currentFilter = 'all';
        this.currentSort = 'date';
        this.isLoading = false;
        this.init();
    }

    async init() {
        await this.loadProjects();
        this.setupFilters();
        this.setupSearch();
        this.setupSort();
        this.setupAnimations();
        this.renderProjects();
    }

    // Load projects from API or static data
    async loadProjects() {
        this.isLoading = true;
        this.showLoadingState();

        try {
            // Try to load from API first
            const response = await fetch('/api/portfolio');
            if (response.ok) {
                const data = await response.json();
                this.projects = data.portfolio || [];
            } else {
                // Fallback to static data
                this.loadStaticProjects();
            }
        } catch (error) {
            console.log('Loading static portfolio data');
            this.loadStaticProjects();
        }

        this.isLoading = false;
        this.hideLoadingState();
    }

    loadStaticProjects() {
        this.projects = [
            {
                id: 1,
                title: 'E-Ticaret Sitesi',
                description: 'Modern e-ticaret platformu. Ödeme sistemi, ürün katalog yönetimi ve admin paneli ile tam özellikli online mağaza çözümü.',
                category: 'ecommerce',
                technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
                image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&w=600&h=400&fit=crop',
                demoUrl: 'demo-ecommerce.html',
                githubUrl: '#',
                client: 'TechStore',
                duration: '8 hafta',
                year: '2024',
                featured: true,
                status: 'completed'
            },
            {
                id: 2,
                title: 'Kurumsal Website',
                description: 'Profesyonel kurumsal web sitesi. CMS, çoklu dil desteği ve SEO optimizasyonu ile şirket imajını güçlendiren platform.',
                category: 'corporate',
                technologies: ['HTML5', 'CSS3', 'JavaScript', 'PHP'],
                image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&w=600&h=400&fit=crop',
                demoUrl: 'demo-corporate.html',
                githubUrl: '#',
                client: 'İnovasyon Teknoloji',
                duration: '6 hafta',
                year: '2024',
                featured: true,
                status: 'completed'
            },
            {
                id: 3,
                title: 'Restaurant Website',
                description: 'Modern restaurant web sitesi. Online rezervasyon, dijital menü ve WhatsApp sipariş sistemi ile müşteri deneyimini artıran platform.',
                category: 'restaurant',
                technologies: ['React', 'Node.js', 'WhatsApp API'],
                image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&w=600&h=400&fit=crop',
                demoUrl: 'demo-restaurant.html',
                githubUrl: '#',
                client: 'Lezzet Durağı',
                duration: '4 hafta',
                year: '2024',
                featured: false,
                status: 'completed'
            },
            {
                id: 4,
                title: 'Portfolio Website',
                description: 'Kişisel portfolio web sitesi. React SPA, blog sistemi ve dark/light tema desteği ile profesyonel kişisel marka platformu.',
                category: 'portfolio',
                technologies: ['React', 'Next.js', 'Tailwind CSS'],
                image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&w=600&h=400&fit=crop',
                demoUrl: 'demo-portfolio.html',
                githubUrl: '#',
                client: 'Freelance Developer',
                duration: '5 hafta',
                year: '2024',
                featured: false,
                status: 'completed'
            },
            {
                id: 5,
                title: 'Mobile App Landing',
                description: 'Mobil uygulama tanıtım sitesi. App Store ve Google Play indirme linkleri, özellik showcase ve kullanıcı yorumları.',
                category: 'mobile',
                technologies: ['Vue.js', 'Nuxt.js', 'SCSS'],
                image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-4.0.3&w=600&h=400&fit=crop',
                demoUrl: '#',
                githubUrl: '#',
                client: 'MobileApp Co.',
                duration: '3 hafta',
                year: '2024',
                featured: false,
                status: 'in-progress'
            },
            {
                id: 6,
                title: 'Blog Platform',
                description: 'Modern blog platformu. Markdown desteği, kategori sistemi, yorum yönetimi ve SEO optimizasyonu ile tam özellikli blog çözümü.',
                category: 'blog',
                technologies: ['Gatsby', 'GraphQL', 'Contentful'],
                image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&w=600&h=400&fit=crop',
                demoUrl: '#',
                githubUrl: '#',
                client: 'Tech Blogger',
                duration: '6 hafta',
                year: '2023',
                featured: false,
                status: 'completed'
            }
        ];
    }

    // Setup filter functionality
    setupFilters() {
        const filterContainer = document.getElementById('portfolioFilters');
        if (!filterContainer) {
            this.createFilterContainer();
        }

        // Create filter buttons
        const categories = ['all', ...new Set(this.projects.map(p => p.category))];
        const filterButtons = categories.map(category => {
            const isActive = category === this.currentFilter ? 'active' : '';
            const categoryName = this.getCategoryName(category);
            
            return `
                <button class="btn btn-outline-primary me-2 mb-2 filter-btn ${isActive}" 
                        data-filter="${category}">
                    ${categoryName}
                </button>
            `;
        }).join('');

        const filtersHtml = `
            <div class="portfolio-filters text-center mb-4">
                <h5 class="mb-3">Kategori Filtresi:</h5>
                ${filterButtons}
            </div>
        `;

        const portfolioSection = document.getElementById('portfolio');
        if (portfolioSection) {
            const existingFilters = portfolioSection.querySelector('.portfolio-filters');
            if (existingFilters) {
                existingFilters.remove();
            }
            portfolioSection.insertAdjacentHTML('afterbegin', filtersHtml);
        }

        // Add event listeners
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filter = e.target.getAttribute('data-filter');
                this.setFilter(filter);
            });
        });
    }

    getCategoryName(category) {
        const categoryNames = {
            'all': 'Tümü',
            'ecommerce': 'E-Ticaret',
            'corporate': 'Kurumsal',
            'restaurant': 'Restaurant',
            'portfolio': 'Portfolio',
            'mobile': 'Mobil',
            'blog': 'Blog'
        };
        return categoryNames[category] || category;
    }

    // Setup search functionality
    setupSearch() {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'portfolio-search text-center mb-4';
        searchContainer.innerHTML = `
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" id="portfolioSearch" 
                               placeholder="Proje ara...">
                        <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        const portfolioSection = document.getElementById('portfolio');
        if (portfolioSection) {
            const filtersElement = portfolioSection.querySelector('.portfolio-filters');
            if (filtersElement) {
                filtersElement.insertAdjacentElement('afterend', searchContainer);
            }
        }

        // Add search functionality
        const searchInput = document.getElementById('portfolioSearch');
        const clearButton = document.getElementById('clearSearch');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchProjects(e.target.value);
            });
        }

        if (clearButton) {
            clearButton.addEventListener('click', () => {
                searchInput.value = '';
                this.searchProjects('');
            });
        }
    }

    // Setup sort functionality
    setupSort() {
        const sortContainer = document.createElement('div');
        sortContainer.className = 'portfolio-sort text-center mb-4';
        sortContainer.innerHTML = `
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <select class="form-select" id="portfolioSort">
                        <option value="date">Tarihe Göre</option>
                        <option value="title">İsme Göre</option>
                        <option value="category">Kategoriye Göre</option>
                        <option value="featured">Öne Çıkanlar</option>
                    </select>
                </div>
            </div>
        `;

        const portfolioSection = document.getElementById('portfolio');
        if (portfolioSection) {
            const searchElement = portfolioSection.querySelector('.portfolio-search');
            if (searchElement) {
                searchElement.insertAdjacentElement('afterend', sortContainer);
            }
        }

        // Add sort functionality
        const sortSelect = document.getElementById('portfolioSort');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.setSortOrder(e.target.value);
            });
        }
    }

    // Setup animations
    setupAnimations() {
        // Intersection Observer for scroll animations
        if ('IntersectionObserver' in window) {
            this.portfolioObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });
        }
    }

    // Set filter
    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-filter') === filter) {
                btn.classList.add('active');
            }
        });

        this.renderProjects();
    }

    // Search projects
    searchProjects(query) {
        this.searchQuery = query.toLowerCase();
        this.renderProjects();
    }

    // Set sort order
    setSortOrder(sortBy) {
        this.currentSort = sortBy;
        this.renderProjects();
    }

    // Filter and sort projects
    getFilteredProjects() {
        let filtered = [...this.projects];

        // Apply category filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(project => project.category === this.currentFilter);
        }

        // Apply search filter
        if (this.searchQuery) {
            filtered = filtered.filter(project => 
                project.title.toLowerCase().includes(this.searchQuery) ||
                project.description.toLowerCase().includes(this.searchQuery) ||
                project.technologies.some(tech => tech.toLowerCase().includes(this.searchQuery))
            );
        }

        // Apply sorting
        filtered.sort((a, b) => {
            switch (this.currentSort) {
                case 'title':
                    return a.title.localeCompare(b.title);
                case 'category':
                    return a.category.localeCompare(b.category);
                case 'featured':
                    return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
                case 'date':
                default:
                    return new Date(b.year) - new Date(a.year);
            }
        });

        return filtered;
    }

    // Render projects
    renderProjects() {
        const container = document.getElementById('portfolioContainer');
        if (!container) return;

        const filteredProjects = this.getFilteredProjects();

        if (filteredProjects.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center">
                    <div class="no-results">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>Sonuç bulunamadı</h4>
                        <p class="text-muted">Arama kriterlerinizi değiştirmeyi deneyin.</p>
                    </div>
                </div>
            `;
            return;
        }

        // Animate out existing items
        const existingItems = container.querySelectorAll('.portfolio-item');
        existingItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '0';
                item.style.transform = 'scale(0.8)';
            }, index * 50);
        });

        // Render new items after animation
        setTimeout(() => {
            const projectsHtml = filteredProjects.map((project, index) => 
                this.createProjectCard(project, index)
            ).join('');

            container.innerHTML = projectsHtml;

            // Setup observers for new items
            if (this.portfolioObserver) {
                container.querySelectorAll('.portfolio-item').forEach(item => {
                    this.portfolioObserver.observe(item);
                });
            }

            // Animate in new items
            container.querySelectorAll('.portfolio-item').forEach((item, index) => {
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'scale(1)';
                }, index * 100);
            });
        }, 300);
    }

    // Create project card HTML
    createProjectCard(project, index) {
        const statusBadge = project.status === 'in-progress' ? 
            '<span class="badge bg-warning text-dark">Devam Ediyor</span>' : 
            '<span class="badge bg-success">Tamamlandı</span>';

        const featuredBadge = project.featured ? 
            '<span class="badge bg-primary position-absolute top-0 start-0 m-2">Öne Çıkan</span>' : '';

        return `
            <div class="col-lg-4 col-md-6 mb-4 portfolio-item" 
                 data-category="${project.category}" 
                 style="opacity: 0; transform: scale(0.8); transition: all 0.3s ease;">
                <div class="card portfolio-card h-100 card-enhanced position-relative">
                    ${featuredBadge}
                    <div class="portfolio-image-container position-relative overflow-hidden">
                        <img src="${project.image}" class="card-img-top portfolio-image" alt="${project.title}">
                        <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                            <div class="portfolio-actions">
                                <a href="${project.demoUrl}" class="btn btn-primary me-2" target="_blank">
                                    <i class="fas fa-eye me-1"></i>Demo
                                </a>
                                <button class="btn btn-outline-light" onclick="portfolioManager.showProjectDetails(${project.id})">
                                    <i class="fas fa-info-circle me-1"></i>Detay
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${project.title}</h5>
                        <p class="card-text">${project.description}</p>
                        <div class="project-meta mb-3">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>${project.client} | 
                                <i class="fas fa-clock me-1"></i>${project.duration} | 
                                <i class="fas fa-calendar me-1"></i>${project.year}
                            </small>
                        </div>
                        <div class="project-technologies mb-3">
                            ${project.technologies.map(tech => 
                                `<span class="badge bg-secondary me-1 mb-1">${tech}</span>`
                            ).join('')}
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            ${statusBadge}
                            <small class="text-muted">${this.getCategoryName(project.category)}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Show project details modal
    showProjectDetails(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (!project) return;

        // Create modal if it doesn't exist
        let modal = document.getElementById('projectModal');
        if (!modal) {
            modal = this.createProjectModal();
        }

        // Update modal content
        this.updateProjectModal(project);

        // Show modal
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }

    createProjectModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'projectModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="projectModalTitle"></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="projectModalBody">
                        <!-- Content will be dynamically inserted -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                        <a href="#" class="btn btn-primary" id="projectModalDemo" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>Canlı Demo
                        </a>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        return modal;
    }

    updateProjectModal(project) {
        document.getElementById('projectModalTitle').textContent = project.title;
        document.getElementById('projectModalDemo').href = project.demoUrl;
        
        const modalBody = document.getElementById('projectModalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <img src="${project.image}" class="img-fluid rounded mb-3" alt="${project.title}">
                </div>
                <div class="col-md-6">
                    <h6>Proje Detayları</h6>
                    <p>${project.description}</p>
                    
                    <h6>Müşteri</h6>
                    <p>${project.client}</p>
                    
                    <h6>Süre</h6>
                    <p>${project.duration}</p>
                    
                    <h6>Yıl</h6>
                    <p>${project.year}</p>
                    
                    <h6>Teknolojiler</h6>
                    <div class="mb-3">
                        ${project.technologies.map(tech => 
                            `<span class="badge bg-primary me-1 mb-1">${tech}</span>`
                        ).join('')}
                    </div>
                    
                    <h6>Durum</h6>
                    <p>
                        ${project.status === 'completed' ? 
                            '<span class="badge bg-success">Tamamlandı</span>' : 
                            '<span class="badge bg-warning text-dark">Devam Ediyor</span>'
                        }
                    </p>
                </div>
            </div>
        `;
    }

    // Show loading state
    showLoadingState() {
        const container = document.getElementById('portfolioContainer');
        if (container) {
            container.innerHTML = `
                <div class="col-12 text-center">
                    <div class="loading-spinner mx-auto mb-3"></div>
                    <p>Projeler yükleniyor...</p>
                </div>
            `;
        }
    }

    // Hide loading state
    hideLoadingState() {
        // Loading state will be replaced by renderProjects()
    }
}

// Initialize portfolio manager
document.addEventListener('DOMContentLoaded', () => {
    window.portfolioManager = new PortfolioManager();
});

// CSS for portfolio animations
const portfolioCSS = `
.portfolio-item {
    transition: all 0.3s ease;
}

.portfolio-card {
    overflow: hidden;
    transition: all 0.3s ease;
}

.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.portfolio-image-container {
    position: relative;
    height: 250px;
}

.portfolio-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.portfolio-overlay {
    background: rgba(0,0,0,0.8);
    opacity: 0;
    transition: all 0.3s ease;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-card:hover .portfolio-image {
    transform: scale(1.1);
}

.portfolio-actions {
    text-align: center;
}

.filter-btn {
    transition: all 0.3s ease;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.no-results {
    padding: 3rem 1rem;
}

.animate-in {
    opacity: 1 !important;
    transform: scale(1) !important;
}

@media (max-width: 768px) {
    .portfolio-card:hover {
        transform: none;
    }
    
    .portfolio-overlay {
        opacity: 1;
        background: rgba(0,0,0,0.6);
    }
}
`;

// Inject portfolio CSS
const portfolioStyle = document.createElement('style');
portfolioStyle.textContent = portfolioCSS;
document.head.appendChild(portfolioStyle);
