# Broxy Code - Netlify Deployment Guide

## 🚀 Netlify'a Deploy Etme Rehberi

### 1. **GitHub Repository Hazırlığı**

```bash
# Tüm değişiklikleri commit edin
git add .
git commit -m "Prepare for Netlify deployment with admin panel and functions"
git push origin main
```

### 2. **Netlify'da Site Oluşturma**

1. [netlify.com](https://netlify.com) adresine gidin
2. **"New site from Git"** butonuna tıklayın
3. **GitHub** seçin ve repository'nizi seçin
4. **Deploy settings:**
   - **Branch:** `main`
   - **Build command:** `echo 'Static site - no build required'`
   - **Publish directory:** `.` (root)

### 3. **Environment Variables Ayarlama**

Netlify Dashboard > Site Settings > Environment Variables:

```
MONGODB_URI = mongodb+srv://username:<EMAIL>/broxycode?retryWrites=true&w=majority
```

**MongoDB Atlas Setup:**
1. [MongoDB Atlas](https://cloud.mongodb.com) hesabı oluşturun
2. Cluster oluşturun (Free tier yeterli)
3. Database user ekleyin
4. Network Access: `0.0.0.0/0` (Netlify için)
5. Connect > Connect your application
6. Connection string'i kopyalayın

### 4. **Domain Ayarlama (Opsiyonel)**

Netlify Dashboard > Domain Settings:
- **Site name:** `broxycode` (broxycode.netlify.app)
- **Custom domain:** Kendi domain'inizi ekleyebilirsiniz

### 5. **Admin Panel Erişimi**

**Demo Credentials:**
- **Username:** `admin`
- **Password:** `broxy123`

**Veya:**
- **Username:** `yilmaz`
- **Password:** `broxycode2024`

**Admin Panel URL:** `https://broxycode.netlify.app/admin.html`

### 6. **Netlify Functions Test**

Deploy edildikten sonra test edin:

```bash
# Contact form test
curl -X POST https://broxycode.netlify.app/.netlify/functions/contact \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","email":"<EMAIL>","message":"Test message"}'

# Analytics test
curl https://broxycode.netlify.app/.netlify/functions/analytics

# Portfolio test
curl https://broxycode.netlify.app/.netlify/functions/portfolio
```

### 7. **Özellikler**

#### ✅ **Çalışan Admin Panel:**
- **Secure login** with localStorage persistence
- **Real-time analytics** dashboard
- **Contact message management**
- **Portfolio project management**
- **Responsive design** for mobile admin

#### ✅ **Netlify Functions:**
- **Contact form** handling with MongoDB
- **Analytics tracking** with real data
- **Portfolio CRUD** operations
- **CORS enabled** for all origins

#### ✅ **Security Features:**
- **Admin authentication** required
- **Environment variables** for sensitive data
- **HTTPS** by default on Netlify
- **Security headers** configured

#### ✅ **Performance:**
- **CDN** distribution worldwide
- **Automatic HTTPS** and SSL
- **Gzip compression** enabled
- **Caching** optimized

### 8. **Monitoring & Analytics**

#### **Netlify Analytics:**
- Site traffic ve performance metrics
- Function invocation logs
- Error tracking

#### **Admin Panel Analytics:**
- Real-time visitor tracking
- Page view statistics
- Contact form submissions
- Daily/weekly reports

### 9. **Maintenance**

#### **Database Backup:**
```bash
# MongoDB Atlas otomatik backup yapar
# Manuel backup için MongoDB Compass kullanın
```

#### **Function Logs:**
```bash
# Netlify CLI ile logs görüntüleme
netlify functions:log
```

#### **Site Updates:**
```bash
# Git push ile otomatik deploy
git add .
git commit -m "Update content"
git push origin main
```

### 10. **Troubleshooting**

#### **Common Issues:**

**Function Timeout:**
- Netlify Functions 10 saniye timeout'u var
- MongoDB connection pooling kullanın

**Environment Variables:**
- Netlify Dashboard'da doğru ayarlandığından emin olun
- Redeploy gerekebilir

**CORS Errors:**
- Functions'larda CORS headers doğru ayarlanmış
- Preflight requests handle ediliyor

**Admin Login Issues:**
- localStorage clear edin
- Demo credentials kullanın

### 11. **Production Checklist**

- ✅ MongoDB Atlas cluster aktif
- ✅ Environment variables ayarlanmış
- ✅ Domain SSL sertifikası aktif
- ✅ Admin panel erişilebilir
- ✅ Contact form çalışıyor
- ✅ Analytics tracking aktif
- ✅ Demo pages çalışıyor
- ✅ Mobile responsive test edilmiş

### 12. **Support**

**Admin Panel Issues:**
- Username/Password: Demo credentials kullanın
- Clear browser cache and localStorage

**Function Errors:**
- Check Netlify function logs
- Verify MongoDB connection

**General Issues:**
- Check Netlify deploy logs
- Verify environment variables

---

## 🎉 Deployment Tamamlandı!

Site URL: `https://broxycode.netlify.app`
Admin Panel: `https://broxycode.netlify.app/admin.html`

**Next Steps:**
1. MongoDB Atlas setup
2. Environment variables configuration
3. Admin panel test
4. Contact form test
5. Analytics verification
