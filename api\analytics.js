const { MongoClient } = require('mongodb');

let cachedClient = null;

async function connectToDatabase() {
    if (cachedClient) {
        return cachedClient;
    }

    const client = new MongoClient(process.env.MONGODB_URI || 'mongodb+srv://broxycode:<EMAIL>/broxycode?retryWrites=true&w=majority');
    await client.connect();
    cachedClient = client;
    return client;
}

module.exports = async (req, res) => {
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method === 'POST') {
        try {
            const { type, page, userAgent, referrer } = req.body;

            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const collection = db.collection('analytics');

            // Track page visit
            const analyticsData = {
                type: type || 'page_view',
                page: page || '/',
                timestamp: new Date(),
                ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
                userAgent: userAgent || req.headers['user-agent'],
                referrer: referrer || req.headers['referer'],
                country: req.headers['cf-ipcountry'] || 'Unknown',
                device: getDeviceType(userAgent || req.headers['user-agent'])
            };

            await collection.insertOne(analyticsData);

            res.status(200).json({
                success: true,
                message: 'Analytics tracked'
            });

        } catch (error) {
            console.error('Analytics error:', error);
            res.status(500).json({
                success: false,
                error: 'Server error'
            });
        }
    } else if (req.method === 'GET') {
        try {
            const client = await connectToDatabase();
            const db = client.db('broxycode');
            const analyticsCollection = db.collection('analytics');
            const contactsCollection = db.collection('contacts');

            // Get today's date
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const startOfWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

            // Get statistics
            const [
                totalVisits,
                todayVisits,
                weeklyVisits,
                monthlyVisits,
                totalContacts,
                newContacts,
                deviceStats,
                pageStats,
                recentVisits
            ] = await Promise.all([
                analyticsCollection.countDocuments({ type: 'page_view' }),
                analyticsCollection.countDocuments({
                    type: 'page_view',
                    timestamp: { $gte: startOfDay }
                }),
                analyticsCollection.countDocuments({
                    type: 'page_view',
                    timestamp: { $gte: startOfWeek }
                }),
                analyticsCollection.countDocuments({
                    type: 'page_view',
                    timestamp: { $gte: startOfMonth }
                }),
                contactsCollection.countDocuments({}),
                contactsCollection.countDocuments({
                    createdAt: { $gte: startOfWeek }
                }),
                analyticsCollection.aggregate([
                    { $match: { type: 'page_view' } },
                    { $group: { _id: '$device', count: { $sum: 1 } } }
                ]).toArray(),
                analyticsCollection.aggregate([
                    { $match: { type: 'page_view' } },
                    { $group: { _id: '$page', count: { $sum: 1 } } },
                    { $sort: { count: -1 } },
                    { $limit: 5 }
                ]).toArray(),
                analyticsCollection.find({ type: 'page_view' })
                    .sort({ timestamp: -1 })
                    .limit(10)
                    .toArray()
            ]);

            // Get daily visits for the last 30 days
            const dailyVisits = await analyticsCollection.aggregate([
                {
                    $match: {
                        type: 'page_view',
                        timestamp: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
                    }
                },
                {
                    $group: {
                        _id: {
                            year: { $year: '$timestamp' },
                            month: { $month: '$timestamp' },
                            day: { $dayOfMonth: '$timestamp' }
                        },
                        count: { $sum: 1 }
                    }
                },
                { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
            ]).toArray();

            res.status(200).json({
                success: true,
                data: {
                    overview: {
                        totalVisits,
                        todayVisits,
                        weeklyVisits,
                        monthlyVisits,
                        totalContacts,
                        newContacts
                    },
                    deviceStats,
                    pageStats,
                    dailyVisits,
                    recentVisits
                }
            });

        } catch (error) {
            console.error('Get analytics error:', error);
            res.status(500).json({
                success: false,
                error: 'Server error'
            });
        }
    } else {
        res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }
};

function getDeviceType(userAgent) {
    if (!userAgent) return 'Unknown';

    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
        return 'Mobile';
    } else if (/Tablet/.test(userAgent)) {
        return 'Tablet';
    } else {
        return 'Desktop';
    }
}