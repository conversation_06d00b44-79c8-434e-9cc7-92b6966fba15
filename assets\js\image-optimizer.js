// Broxy Code - Image Optimization and WebP Support
// Lazy loading and modern image formats

class ImageOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupWebPSupport();
        this.setupImageEffects();
    }

    // Enhanced lazy loading with Intersection Observer
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe all images with data-src
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for older browsers
            this.loadAllImages();
        }
    }

    loadImage(img) {
        // Create a new image element to preload
        const imageLoader = new Image();
        
        imageLoader.onload = () => {
            // Image loaded successfully
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            img.classList.add('loaded');
            
            // Add fade-in effect
            img.style.opacity = '0';
            img.style.transition = 'opacity 0.3s ease';
            
            setTimeout(() => {
                img.style.opacity = '1';
            }, 50);
        };

        imageLoader.onerror = () => {
            // Fallback if image fails to load
            img.src = img.dataset.fallback || 'assets/images/placeholder.jpg';
            img.classList.add('error');
        };

        // Start loading the image
        imageLoader.src = img.dataset.src;
    }

    loadAllImages() {
        document.querySelectorAll('img[data-src]').forEach(img => {
            this.loadImage(img);
        });
    }

    // WebP support detection and implementation
    setupWebPSupport() {
        const supportsWebP = this.checkWebPSupport();
        
        if (supportsWebP) {
            document.documentElement.classList.add('webp');
            this.convertToWebP();
        } else {
            document.documentElement.classList.add('no-webp');
        }
    }

    checkWebPSupport() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }

    convertToWebP() {
        document.querySelectorAll('img[data-webp]').forEach(img => {
            if (img.dataset.webp) {
                img.dataset.src = img.dataset.webp;
            }
        });
    }

    // Image effects and animations
    setupImageEffects() {
        this.setupHoverEffects();
        this.setupParallaxImages();
        this.setupImageReveal();
    }

    setupHoverEffects() {
        document.querySelectorAll('.image-hover-effect').forEach(img => {
            img.addEventListener('mouseenter', () => {
                img.style.transform = 'scale(1.05)';
                img.style.filter = 'brightness(1.1)';
            });

            img.addEventListener('mouseleave', () => {
                img.style.transform = 'scale(1)';
                img.style.filter = 'brightness(1)';
            });
        });
    }

    setupParallaxImages() {
        if (window.innerWidth > 768) {
            const parallaxImages = document.querySelectorAll('.parallax-image');
            
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                
                parallaxImages.forEach(img => {
                    const speed = img.dataset.speed || 0.5;
                    const yPos = -(scrolled * speed);
                    img.style.transform = `translateY(${yPos}px)`;
                });
            }, { passive: true });
        }
    }

    setupImageReveal() {
        if ('IntersectionObserver' in window) {
            const revealObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('revealed');
                    }
                });
            }, {
                threshold: 0.1
            });

            document.querySelectorAll('.image-reveal').forEach(img => {
                revealObserver.observe(img);
            });
        }
    }
}

// Progressive image loading
class ProgressiveImageLoader {
    constructor() {
        this.loadProgressiveImages();
    }

    loadProgressiveImages() {
        document.querySelectorAll('.progressive-image').forEach(container => {
            const img = container.querySelector('img');
            const placeholder = container.querySelector('.placeholder');
            
            if (img && placeholder) {
                this.loadProgressiveImage(img, placeholder);
            }
        });
    }

    loadProgressiveImage(img, placeholder) {
        // Load low-quality placeholder first
        if (img.dataset.placeholder) {
            placeholder.style.backgroundImage = `url(${img.dataset.placeholder})`;
            placeholder.style.filter = 'blur(5px)';
        }

        // Load high-quality image
        const highQualityImage = new Image();
        
        highQualityImage.onload = () => {
            img.src = highQualityImage.src;
            img.style.opacity = '1';
            
            // Fade out placeholder
            placeholder.style.opacity = '0';
            
            setTimeout(() => {
                placeholder.style.display = 'none';
            }, 300);
        };

        highQualityImage.src = img.dataset.src;
    }
}

// Image compression and optimization
class ImageCompressor {
    constructor() {
        this.setupImageCompression();
    }

    setupImageCompression() {
        // Monitor image loading performance
        if ('PerformanceObserver' in window) {
            const imageObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.initiatorType === 'img') {
                        this.analyzeImagePerformance(entry);
                    }
                });
            });

            imageObserver.observe({ entryTypes: ['resource'] });
        }
    }

    analyzeImagePerformance(entry) {
        const size = entry.transferSize;
        const duration = entry.duration;
        
        // Log performance metrics for optimization
        if (size > 500000) { // 500KB
            console.warn(`Large image detected: ${entry.name} (${(size / 1024).toFixed(2)}KB)`);
        }
        
        if (duration > 1000) { // 1 second
            console.warn(`Slow loading image: ${entry.name} (${duration.toFixed(2)}ms)`);
        }
    }

    // Client-side image compression (for user uploads)
    compressImage(file, quality = 0.8, maxWidth = 1920) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate new dimensions
                const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
                canvas.width = img.width * ratio;
                canvas.height = img.height * ratio;

                // Draw and compress
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                canvas.toBlob(resolve, 'image/jpeg', quality);
            };

            img.src = URL.createObjectURL(file);
        });
    }
}

// Initialize image optimization
document.addEventListener('DOMContentLoaded', () => {
    new ImageOptimizer();
    new ProgressiveImageLoader();
    new ImageCompressor();
});

// CSS for image effects
const imageCSS = `
.lazy {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.loaded {
    opacity: 1;
}

.image-hover-effect {
    transition: all 0.3s ease;
    cursor: pointer;
}

.image-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.image-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.progressive-image {
    position: relative;
    overflow: hidden;
}

.progressive-image .placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    transition: opacity 0.3s ease;
}

.progressive-image img {
    opacity: 0;
    transition: opacity 0.3s ease;
    width: 100%;
    height: auto;
}

.parallax-image {
    will-change: transform;
}

/* WebP support styles */
.webp .bg-image {
    background-image: url('image.webp');
}

.no-webp .bg-image {
    background-image: url('image.jpg');
}
`;

// Inject image CSS
const imageStyle = document.createElement('style');
imageStyle.textContent = imageCSS;
document.head.appendChild(imageStyle);
