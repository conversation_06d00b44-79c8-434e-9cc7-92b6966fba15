/* Custom CSS for Broxy Code - Web Development Company */

/* Enhanced Color Palette */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4ecdc4;
    --warning-color: #ffe066;
    --danger-color: #ff6b6b;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    --shadow-light: 0 5px 15px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.12);
    --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.16);
}

/* Broxy Logo Styles */
.broxy-logo {
    display: inline-flex;
    align-items: center;
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    text-decoration: none;
    color: #fff;
    transition: all 0.3s ease;
}

.logo-icon {
    position: relative;
    width: 50px;
    height: 50px;
    margin-right: 12px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
}

.logo-icon::before {
    content: 'B';
    position: absolute;
    font-size: 24px;
    font-weight: 900;
    color: white;
    z-index: 2;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
}

.logo-icon::after {
    content: 'X';
    position: absolute;
    font-size: 20px;
    font-weight: 900;
    color: rgba(255, 255, 255, 0.8);
    z-index: 1;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}

.broxy-logo:hover .logo-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.broxy-logo:hover {
    color: #007bff;
    text-decoration: none;
}

.logo-text {
    background: linear-gradient(135deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.6rem;
    letter-spacing: 1px;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Colors */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

/* Navigation Styles */
.navbar {
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/></svg>');
    z-index: 1;
}



.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    animation: fadeInUp 1s ease;
    color: #007bff; /* Mavi renk */
}

.hero-content h2 {
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-content p {
    animation: fadeInUp 1s ease 0.4s both;
}

.hero-buttons {
    animation: fadeInUp 1s ease 0.6s both;
}

.code-animation {
    animation: float 3s ease-in-out infinite;
}

.code-animation img {
    transition: all 0.3s ease;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.code-animation:hover img {
    transform: scale(1.05);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    height: 100%;
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card i {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.service-card:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* Founder Info Styling */
.founder-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.founder-info:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

/* Portfolio Section */
.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
}

.portfolio-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
}

.portfolio-image img {
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.05);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    backdrop-filter: blur(10px);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-content {
    text-align: center;
    color: white;
    padding: 2rem;
}

.portfolio-content h4 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.portfolio-tech {
    margin-top: 1rem;
}

/* Contact Section */
.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.contact-item i {
    margin-right: 1rem;
}

.contact-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 10px;
    padding: 0.75rem 1rem;
}

.contact-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.contact-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    color: white;
}

/* Enhanced Button Styles */
.btn {
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
    z-index: -1;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
}

.btn-primary:hover {
    background: var(--gradient-accent);
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-heavy);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-heavy);
    color: white;
}

/* Pulse Animation for CTA Buttons */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

.btn-primary.pulse {
    animation: pulse 2s infinite;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.2rem;
        line-height: 1.2;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }

    .hero-buttons .btn {
        width: 100%;
        margin: 0;
        padding: 1rem 2rem;
    }

    .portfolio-content {
        padding: 1rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        margin-bottom: 2rem;
    }

    .contact-item i {
        margin-bottom: 1rem;
        margin-right: 0;
    }

    .founder-info {
        margin-top: 2rem;
        padding: 2rem 1rem;
    }

    .service-card {
        margin-bottom: 2rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 1.8rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .navbar-brand .logo-text {
        font-size: 1.2rem;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
    }
}

/* Section Spacing */
section {
    padding: 5rem 0;
}

/* Custom Utilities */
.text-gradient {
    background: linear-gradient(45deg, #007bff, #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Loading Animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Portfolio Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1055;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.modal.show {
    display: block !important;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
}

.modal-lg {
    max-width: 800px;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 20px;
    outline: 0;
    box-shadow: var(--shadow-heavy);
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.show {
    opacity: 0.5;
}

.modal-open {
    overflow: hidden;
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 20px 20px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
}

/* Portfolio Loading States */
.portfolio-loading {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Portfolio Item Styles */
.portfolio-item {
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
    backdrop-filter: blur(10px);
}

.portfolio-overlay .btn {
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease 0.2s;
}

.portfolio-item:hover .portfolio-overlay .btn {
    transform: translateY(0);
    opacity: 1;
}

/* Badge Animations */
.badge {
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.1);
}

/* Modal Animation */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* Responsive Modal */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* Enhanced Portfolio Styles */
.portfolio-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
    border-radius: 15px;
}

.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.portfolio-card .card-img-top {
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.portfolio-card:hover .card-img-top {
    transform: scale(1.05);
}

.portfolio-card .card-body {
    padding: 1.5rem;
}

.portfolio-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.portfolio-card .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 15px;
}

.portfolio-filters {
    margin-bottom: 2rem;
}

.portfolio-filters .filter-btn {
    transition: all 0.3s ease;
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    margin: 0.25rem;
}

.portfolio-filters .filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.portfolio-filters .filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.portfolio-search .form-control {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.portfolio-search .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.portfolio-search .btn {
    border-radius: 0 25px 25px 0;
}

.no-results {
    padding: 3rem 1rem;
    text-align: center;
}

.no-results i {
    opacity: 0.5;
}

.portfolio-sort select {
    border-radius: 25px;
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.portfolio-sort select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}