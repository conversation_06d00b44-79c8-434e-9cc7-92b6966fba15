/* Custom CSS for Broxy - Web Development Company */

/* Broxy Logo Styles */
.broxy-logo {
    display: inline-flex;
    align-items: center;
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    text-decoration: none;
    color: #fff;
    transition: all 0.3s ease;
}

.logo-icon {
    position: relative;
    width: 50px;
    height: 50px;
    margin-right: 12px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
}

.logo-icon::before {
    content: 'B';
    position: absolute;
    font-size: 24px;
    font-weight: 900;
    color: white;
    z-index: 2;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
}

.logo-icon::after {
    content: 'X';
    position: absolute;
    font-size: 20px;
    font-weight: 900;
    color: rgba(255, 255, 255, 0.8);
    z-index: 1;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}

.broxy-logo:hover .logo-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.broxy-logo:hover {
    color: #007bff;
    text-decoration: none;
}

.logo-text {
    background: linear-gradient(135deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.6rem;
    letter-spacing: 1px;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Colors */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

/* Navigation Styles */
.navbar {
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    animation: fadeInUp 1s ease;
}

.hero-content h2 {
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-content p {
    animation: fadeInUp 1s ease 0.4s both;
}

.hero-buttons {
    animation: fadeInUp 1s ease 0.6s both;
}

.code-animation {
    animation: float 3s ease-in-out infinite;
}

.code-animation img {
    transition: all 0.3s ease;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.code-animation:hover img {
    transform: scale(1.05);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-card i {
    transition: all 0.3s ease;
}

.service-card:hover i {
    transform: scale(1.1);
}

/* Portfolio Section */
.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
}

.portfolio-image img {
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.05);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-content {
    text-align: center;
    color: white;
    padding: 2rem;
}

.portfolio-content h4 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.portfolio-tech {
    margin-top: 1rem;
}

/* Contact Section */
.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.contact-item i {
    margin-right: 1rem;
}

.contact-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 10px;
    padding: 0.75rem 1rem;
}

.contact-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.contact-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    color: white;
}

/* Button Styles */
.btn {
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 123, 255, 0.3);
}

.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 123, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-buttons .btn {
        width: 100%;
        margin: 0;
    }

    .portfolio-content {
        padding: 1rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-item i {
        margin-bottom: 1rem;
        margin-right: 0;
    }
}

/* Section Spacing */
section {
    padding: 5rem 0;
}

/* Custom Utilities */
.text-gradient {
    background: linear-gradient(45deg, #007bff, #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Loading Animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}