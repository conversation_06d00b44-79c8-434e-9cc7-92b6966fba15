/* Broxy Code - Performance Optimized CSS */
/* Modern visual effects and animations */

/* CSS Custom Properties for theming */
:root {
    /* Primary Colors */
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --primary-light: #8fa4f3;
    --secondary-color: #764ba2;
    --accent-color: #ff6b35;
    --accent-dark: #e55a2b;
    
    /* Text Colors */
    --text-primary: #2d3436;
    --text-secondary: #636e72;
    --text-muted: #b2bec3;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #2d3436;
    --bg-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --bg-gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 30px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 40px rgba(0,0,0,0.15);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --radius-xl: 30px;
    --radius-full: 50px;
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
}

/* Performance optimizations */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Lazy loading images */
img {
    max-width: 100%;
    height: auto;
    transition: opacity var(--transition-normal);
}

img[data-src] {
    opacity: 0;
}

img.loaded {
    opacity: 1;
}

/* Modern gradient text effects */
.gradient-text {
    background: var(--bg-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    position: relative;
}

.gradient-text-accent {
    background: var(--bg-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Animated gradient text */
.gradient-text-animated {
    background: linear-gradient(-45deg, var(--primary-color), var(--secondary-color), var(--accent-color), var(--primary-light));
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Enhanced button animations */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    border: none;
    border-radius: var(--radius-full);
    padding: 12px 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    z-index: 1;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
    z-index: -1;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-enhanced:active {
    transform: translateY(-1px);
}

.btn-primary-enhanced {
    background: var(--bg-gradient);
    color: var(--text-white);
}

.btn-accent-enhanced {
    background: var(--bg-gradient-accent);
    color: var(--text-white);
}

.btn-outline-enhanced {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-enhanced:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

/* Card hover animations */
.card-enhanced {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-gradient);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.card-enhanced:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.card-enhanced:hover::before {
    opacity: 0.05;
}

/* Scroll-triggered animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

.animate-fade-in {
    opacity: 0;
    transition: opacity 1s ease;
}

.animate-fade-in.animated {
    opacity: 1;
}

.animate-slide-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease;
}

.animate-slide-left.animated {
    opacity: 1;
    transform: translateX(0);
}

.animate-slide-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease;
}

.animate-slide-right.animated {
    opacity: 1;
    transform: translateX(0);
}

.animate-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s ease;
}

.animate-scale.animated {
    opacity: 1;
    transform: scale(1);
}

/* Parallax effects */
.parallax-container {
    position: relative;
    overflow: hidden;
}

.parallax-element {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 120%;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    will-change: transform;
}

/* Loading animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-secondary);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pulse animation for loading states */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Floating animation */
.float {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Glow effect */
.glow {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    transition: box-shadow var(--transition-normal);
}

.glow:hover {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
}

/* Text reveal animation */
.text-reveal {
    overflow: hidden;
    position: relative;
}

.text-reveal::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    transform: translateX(-100%);
    animation: reveal 1.5s ease forwards;
}

@keyframes reveal {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

/* Performance optimizations for mobile */
@media (max-width: 768px) {
    .parallax-element {
        background-attachment: scroll;
    }
    
    .card-enhanced:hover {
        transform: none;
    }
    
    .btn-enhanced:hover {
        transform: none;
    }
}

/* Advanced hover effects */
.hover-lift {
    transition: all var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.hover-glow {
    transition: all var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.4);
    transform: scale(1.02);
}

.hover-rotate {
    transition: all var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg) scale(1.05);
}

/* Advanced gradient backgrounds */
.gradient-bg-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.gradient-bg-2 {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ffd700 100%);
}

.gradient-bg-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-animated {
    background: linear-gradient(-45deg, #667eea, #764ba2, #ff6b35, #4facfe);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
}

/* Morphing shapes */
.morphing-shape {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
    0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
    50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
    100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
}

/* Glass morphism effect */
.glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Neumorphism effect */
.neumorphism {
    background: #f0f0f3;
    border-radius: var(--radius-lg);
    box-shadow:
        20px 20px 60px #bebebe,
        -20px -20px 60px #ffffff;
}

.neumorphism-inset {
    background: #f0f0f3;
    border-radius: var(--radius-lg);
    box-shadow:
        inset 20px 20px 60px #bebebe,
        inset -20px -20px 60px #ffffff;
}

/* Advanced text effects */
.text-shadow-glow {
    text-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.text-outline {
    -webkit-text-stroke: 2px var(--primary-color);
    -webkit-text-fill-color: transparent;
}

.text-3d {
    text-shadow:
        1px 1px 0px var(--primary-dark),
        2px 2px 0px var(--primary-dark),
        3px 3px 0px var(--primary-dark),
        4px 4px 0px var(--primary-dark),
        5px 5px 10px rgba(0,0,0,0.3);
}

/* Particle effects */
.particles-container {
    position: relative;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.7;
    animation: float-particle 6s ease-in-out infinite;
}

@keyframes float-particle {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-100px) rotate(180deg); opacity: 1; }
}

/* Advanced loading states */
.skeleton-loader {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.wave-loader {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: wave-pulse 1.5s ease-in-out infinite;
}

@keyframes wave-pulse {
    0% { transform: scale(0); opacity: 1; }
    100% { transform: scale(1); opacity: 0; }
}

/* Interactive elements */
.magnetic-button {
    transition: all var(--transition-normal);
    cursor: pointer;
}

.magnetic-button:hover {
    transform: scale(1.1);
}

.tilt-card {
    transition: all var(--transition-normal);
    transform-style: preserve-3d;
}

.tilt-card:hover {
    transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}

/* Scroll indicators */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--bg-gradient);
    transform-origin: left;
    transform: scaleX(0);
    z-index: 9999;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .parallax-element {
        transform: none !important;
    }

    .morphing-shape {
        animation: none !important;
    }

    .gradient-bg-animated {
        animation: none !important;
    }
}
